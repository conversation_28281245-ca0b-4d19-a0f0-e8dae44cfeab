{"timestamp": "2025-06-05T18:41:41.603Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:41:46.256Z"}, {"name": "Loading Element Visible", "passed": false, "timestamp": "2025-06-05T18:41:46.381Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:41:46.387Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:41:46.389Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:41:48.605Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:41:49.460Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:41:50.297Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:41:50.324Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:41:43.285Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:41:43.604Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:41:43.722Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:41:43.725Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:41:43.734Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:41:43.734Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:41:43.736Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:41:43.736Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:41:43.737Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:41:43.738Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:41:43.738Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:41:43.739Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:41:43.739Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:41:44.419Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:41:44.421Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:41:44.423Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:41:44.423Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:41:44.423Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:44.423Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:44.423Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:41:44.435Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:41:44.435Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:41:44.435Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:41:44.436Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:41:44.438Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:41:44.445Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:41:44.446Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:41:44.447Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:41:44.447Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:41:44.447Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:41:44.447Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:41:44.573Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:41:44.573Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:44.573Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:41:44.574Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.735Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.762Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.780Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.781Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.782Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.782Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.782Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.783Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.783Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.786Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.787Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.787Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.787Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.788Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.788Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.790Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.796Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.813Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.813Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.815Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.815Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.816Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.816Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.818Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.818Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.818Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.819Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.819Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.819Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.819Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.819Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.820Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.820Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.820Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.820Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.820Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.821Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.821Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.821Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.823Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.823Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.826Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.829Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.830Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.830Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.830Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.831Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:44.831Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:41:44.831Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:41:44.831Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:41:47.580Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:41:47.582Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:41:47.583Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:41:47.584Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:41:47.584Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:41:48.519Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:41:48.522Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:41:48.523Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:41:48.523Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:41:48.523Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:48.523Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:48.523Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:41:48.531Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:41:48.531Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:41:48.531Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:41:48.532Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:41:48.533Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:41:48.620Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:41:48.623Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:48.649Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:41:48.653Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.835Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.848Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.849Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.849Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.853Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.853Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.854Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.854Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.855Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.856Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.857Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.862Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.863Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.863Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.863Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.863Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.863Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.863Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.864Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.864Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.864Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.864Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.864Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.865Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.865Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.865Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.865Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.865Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.866Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.866Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.866Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.866Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.867Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.867Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.868Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.868Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.868Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.869Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.869Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.869Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.869Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.869Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.870Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.871Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.872Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.873Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.913Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.914Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.914Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:41:48.919Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:41:48.921Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:41:48.922Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:41:48.953Z"}, {"type": "log", "text": "[DEBUG] Updating word cloud colors JSHandle@object", "timestamp": "2025-06-05T18:41:48.953Z"}, {"type": "log", "text": "[INFO] Word cloud colors updated JSHandle@object", "timestamp": "2025-06-05T18:41:48.954Z"}, {"type": "log", "text": "[DEBUG] Word hovered JSHandle@object", "timestamp": "2025-06-05T18:41:49.499Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.547Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.555Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.556Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.576Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.581Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.582Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.589Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.596Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.604Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.622Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.622Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.623Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.635Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.637Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.637Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.653Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.654Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.655Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.665Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.667Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.667Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.673Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.675Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.676Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.699Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.701Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.701Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.758Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.770Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.772Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:41:49.788Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:41:49.788Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:41:49.789Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749148905382.png", "timestamp": "2025-06-05T18:41:46.237Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749148906439.png", "timestamp": "2025-06-05T18:41:47.308Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749148910338.png", "timestamp": "2025-06-05T18:41:51.150Z"}], "errors": [], "performance": {"pageLoad": 2830}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:41:43.591Z", "elapsed": 2, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:41:43.590Z"}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.708Z", "elapsed": 118, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.712Z", "elapsed": 122, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.712Z", "elapsed": 122, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.713Z", "elapsed": 123, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.713Z", "elapsed": 123, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.713Z", "elapsed": 123, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.714Z", "elapsed": 124, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.714Z", "elapsed": 124, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.714Z", "elapsed": 124, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.716Z", "elapsed": 126, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:43.717Z", "elapsed": 127, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.407Z", "elapsed": 817, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 706996, "dataType": "object", "isArray": true, "searchQuery": "neural"}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.410Z", "elapsed": 820, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 136, "processedCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.411Z", "elapsed": 821, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 20, "fetchTimeMs": 698}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.411Z", "elapsed": 821, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.412Z", "elapsed": 822, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.414Z", "elapsed": 824, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 24936}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.414Z", "elapsed": 824, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3525}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.418Z", "elapsed": 828, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1094, "topWords": 50, "mostFrequent": "neural"}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.419Z", "elapsed": 829, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 7}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.419Z", "elapsed": 829, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 20, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.420Z", "elapsed": 830, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.421Z", "elapsed": 831, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.422Z", "elapsed": 832, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.422Z", "elapsed": 832, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.423Z", "elapsed": 833, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.429Z", "elapsed": 839, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.430Z", "elapsed": 840, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 9}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.431Z", "elapsed": 841, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 718}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.539Z", "elapsed": 949, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.539Z", "elapsed": 949, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.540Z", "elapsed": 950, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.540Z", "elapsed": 950, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.704Z", "elapsed": 1114, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 313, "y": 270}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.707Z", "elapsed": 1118, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 440, "y": 333}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.713Z", "elapsed": 1123, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fields", "x": 559, "y": 181}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.714Z", "elapsed": 1124, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 643, "y": 323}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.716Z", "elapsed": 1126, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 817, "y": 164}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.717Z", "elapsed": 1127, "level": "DEBUG", "message": "Word positioned", "data": {"text": "radiance", "x": 372, "y": 151}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.718Z", "elapsed": 1128, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nerf", "x": 796, "y": 348}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.719Z", "elapsed": 1129, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 794, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.721Z", "elapsed": 1131, "level": "DEBUG", "message": "Word positioned", "data": {"text": "reconstruction", "x": 865, "y": 117}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.722Z", "elapsed": 1132, "level": "DEBUG", "message": "Word positioned", "data": {"text": "dataset", "x": 389, "y": 284}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.727Z", "elapsed": 1137, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 557, "y": 305}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.730Z", "elapsed": 1140, "level": "DEBUG", "message": "Word positioned", "data": {"text": "rendering", "x": 344, "y": 380}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.732Z", "elapsed": 1142, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 445, "y": 207}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.733Z", "elapsed": 1143, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 811, "y": 417}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.734Z", "elapsed": 1144, "level": "DEBUG", "message": "Word positioned", "data": {"text": "approach", "x": 262, "y": 370}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.735Z", "elapsed": 1145, "level": "DEBUG", "message": "Word positioned", "data": {"text": "depth", "x": 666, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.735Z", "elapsed": 1145, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 735, "y": 298}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.736Z", "elapsed": 1146, "level": "DEBUG", "message": "Word positioned", "data": {"text": "video", "x": 234, "y": 184}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.737Z", "elapsed": 1147, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scene", "x": 570, "y": 117}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.737Z", "elapsed": 1147, "level": "DEBUG", "message": "Word positioned", "data": {"text": "method", "x": 842, "y": 252}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.740Z", "elapsed": 1150, "level": "DEBUG", "message": "Word positioned", "data": {"text": "using", "x": 549, "y": 371}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.743Z", "elapsed": 1153, "level": "DEBUG", "message": "Word positioned", "data": {"text": "images", "x": 476, "y": 90}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.744Z", "elapsed": 1154, "level": "DEBUG", "message": "Word positioned", "data": {"text": "research", "x": 353, "y": 102}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.745Z", "elapsed": 1155, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 966, "y": 207}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.746Z", "elapsed": 1156, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 1025, "y": 169}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.747Z", "elapsed": 1157, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 906, "y": 308}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.748Z", "elapsed": 1158, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 622, "y": 174}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.748Z", "elapsed": 1158, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 504, "y": 431}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.749Z", "elapsed": 1159, "level": "DEBUG", "message": "Word positioned", "data": {"text": "methods", "x": 853, "y": 73}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.750Z", "elapsed": 1160, "level": "DEBUG", "message": "Word positioned", "data": {"text": "temporal", "x": 171, "y": 356}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.750Z", "elapsed": 1160, "level": "DEBUG", "message": "Word positioned", "data": {"text": "shape", "x": 1002, "y": 289}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.751Z", "elapsed": 1161, "level": "DEBUG", "message": "Word positioned", "data": {"text": "time", "x": 713, "y": 345}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.752Z", "elapsed": 1162, "level": "DEBUG", "message": "Word positioned", "data": {"text": "different", "x": 83, "y": 343}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.752Z", "elapsed": 1162, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 303, "y": 200}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.753Z", "elapsed": 1163, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learned", "x": 145, "y": 203}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.754Z", "elapsed": 1164, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multiple", "x": 640, "y": 447}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.755Z", "elapsed": 1165, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 1079, "y": 131}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.756Z", "elapsed": 1166, "level": "DEBUG", "message": "Word positioned", "data": {"text": "via", "x": 840, "y": 339}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.757Z", "elapsed": 1167, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scenes", "x": 1046, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.758Z", "elapsed": 1168, "level": "DEBUG", "message": "Word positioned", "data": {"text": "work", "x": 302, "y": 355}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.761Z", "elapsed": 1171, "level": "DEBUG", "message": "Word positioned", "data": {"text": "changes", "x": 938, "y": 372}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.762Z", "elapsed": 1172, "level": "DEBUG", "message": "Word positioned", "data": {"text": "over", "x": 186, "y": 263}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.763Z", "elapsed": 1173, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representations", "x": 1117, "y": 350}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.766Z", "elapsed": 1176, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nerfs", "x": 1078, "y": 291}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.768Z", "elapsed": 1178, "level": "DEBUG", "message": "Word positioned", "data": {"text": "existing", "x": 410, "y": 429}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.770Z", "elapsed": 1180, "level": "DEBUG", "message": "Word positioned", "data": {"text": "demonstrate", "x": 108, "y": 88}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.773Z", "elapsed": 1183, "level": "DEBUG", "message": "Word positioned", "data": {"text": "geometry", "x": 219, "y": 348}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.774Z", "elapsed": 1184, "level": "DEBUG", "message": "Word positioned", "data": {"text": "text", "x": 762, "y": 334}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.776Z", "elapsed": 1186, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 48}, "stack": null}, {"timestamp": "2025-06-05T18:41:44.785Z", "elapsed": 1195, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:47.559Z", "elapsed": 3970, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:47.561Z", "elapsed": 3971, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:47.562Z", "elapsed": 3972, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:47.562Z", "elapsed": 3972, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:47.563Z", "elapsed": 3973, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.496Z", "elapsed": 4906, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 1396717, "dataType": "object", "isArray": true, "searchQuery": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.499Z", "elapsed": 4909, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 250, "processedCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.500Z", "elapsed": 4910, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 20, "fetchTimeMs": 938}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.500Z", "elapsed": 4910, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.501Z", "elapsed": 4911, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.502Z", "elapsed": 4912, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 26066}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.504Z", "elapsed": 4914, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3665}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.507Z", "elapsed": 4917, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1200, "topWords": 50, "mostFrequent": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.511Z", "elapsed": 4921, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 11}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.512Z", "elapsed": 4922, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 20, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.513Z", "elapsed": 4923, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.514Z", "elapsed": 4924, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.514Z", "elapsed": 4924, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.515Z", "elapsed": 4925, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.516Z", "elapsed": 4926, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.519Z", "elapsed": 4929, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.520Z", "elapsed": 4930, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 6}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.520Z", "elapsed": 4930, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 959}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.617Z", "elapsed": 5027, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.618Z", "elapsed": 5028, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.618Z", "elapsed": 5028, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.619Z", "elapsed": 5029, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.778Z", "elapsed": 5188, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 350, "y": 238}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.779Z", "elapsed": 5189, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 474, "y": 323}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.781Z", "elapsed": 5191, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 599, "y": 203}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.783Z", "elapsed": 5193, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 246, "y": 246}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.783Z", "elapsed": 5194, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 410, "y": 157}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.785Z", "elapsed": 5195, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformers", "x": 806, "y": 263}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.786Z", "elapsed": 5196, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 700, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.788Z", "elapsed": 5198, "level": "DEBUG", "message": "Word positioned", "data": {"text": "performance", "x": 577, "y": 150}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.789Z", "elapsed": 5199, "level": "DEBUG", "message": "Word positioned", "data": {"text": "layers", "x": 279, "y": 143}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.789Z", "elapsed": 5199, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 838, "y": 205}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.790Z", "elapsed": 5200, "level": "DEBUG", "message": "Word positioned", "data": {"text": "vision", "x": 293, "y": 364}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.794Z", "elapsed": 5204, "level": "DEBUG", "message": "Word positioned", "data": {"text": "show", "x": 724, "y": 175}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.796Z", "elapsed": 5206, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 797, "y": 351}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.797Z", "elapsed": 5207, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 874, "y": 147}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.797Z", "elapsed": 5207, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 568, "y": 339}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.798Z", "elapsed": 5208, "level": "DEBUG", "message": "Word positioned", "data": {"text": "their", "x": 772, "y": 120}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.799Z", "elapsed": 5209, "level": "DEBUG", "message": "Word positioned", "data": {"text": "token", "x": 234, "y": 308}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.800Z", "elapsed": 5210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "layer", "x": 559, "y": 101}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.800Z", "elapsed": 5210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tokens", "x": 387, "y": 327}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.801Z", "elapsed": 5211, "level": "DEBUG", "message": "Word positioned", "data": {"text": "input", "x": 934, "y": 176}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.802Z", "elapsed": 5212, "level": "DEBUG", "message": "Word positioned", "data": {"text": "sequence", "x": 959, "y": 338}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.803Z", "elapsed": 5213, "level": "DEBUG", "message": "Word positioned", "data": {"text": "not", "x": 623, "y": 244}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.803Z", "elapsed": 5213, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 726, "y": 389}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.804Z", "elapsed": 5215, "level": "DEBUG", "message": "Word positioned", "data": {"text": "self-attention", "x": 968, "y": 417}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.806Z", "elapsed": 5216, "level": "DEBUG", "message": "Word positioned", "data": {"text": "but", "x": 510, "y": 243}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.811Z", "elapsed": 5221, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 691, "y": 107}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.813Z", "elapsed": 5223, "level": "DEBUG", "message": "Word positioned", "data": {"text": "inference", "x": 1041, "y": 270}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.814Z", "elapsed": 5224, "level": "DEBUG", "message": "Word positioned", "data": {"text": "feature", "x": 428, "y": 394}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.816Z", "elapsed": 5226, "level": "DEBUG", "message": "Word positioned", "data": {"text": "however", "x": 329, "y": 450}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.818Z", "elapsed": 5228, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer-based", "x": 114, "y": 330}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.819Z", "elapsed": 5229, "level": "DEBUG", "message": "Word positioned", "data": {"text": "network", "x": 255, "y": 196}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.819Z", "elapsed": 5229, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 512, "y": 411}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.820Z", "elapsed": 5230, "level": "DEBUG", "message": "Word positioned", "data": {"text": "lms", "x": 657, "y": 72}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.822Z", "elapsed": 5232, "level": "DEBUG", "message": "Word positioned", "data": {"text": "long", "x": 356, "y": 80}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.822Z", "elapsed": 5233, "level": "DEBUG", "message": "Word positioned", "data": {"text": "work", "x": 184, "y": 319}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.824Z", "elapsed": 5235, "level": "DEBUG", "message": "Word positioned", "data": {"text": "more", "x": 868, "y": 100}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.828Z", "elapsed": 5238, "level": "DEBUG", "message": "Word positioned", "data": {"text": "key", "x": 525, "y": 340}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.830Z", "elapsed": 5240, "level": "DEBUG", "message": "Word positioned", "data": {"text": "outliers", "x": 628, "y": 402}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.831Z", "elapsed": 5241, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learn", "x": 671, "y": 433}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.832Z", "elapsed": 5242, "level": "DEBUG", "message": "Word positioned", "data": {"text": "simple", "x": 238, "y": 394}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.832Z", "elapsed": 5242, "level": "DEBUG", "message": "Word positioned", "data": {"text": "trained", "x": 232, "y": 97}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.833Z", "elapsed": 5243, "level": "DEBUG", "message": "Word positioned", "data": {"text": "rmt", "x": 311, "y": 292}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.834Z", "elapsed": 5244, "level": "DEBUG", "message": "Word positioned", "data": {"text": "into", "x": 462, "y": 445}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.835Z", "elapsed": 5245, "level": "DEBUG", "message": "Word positioned", "data": {"text": "segmentation", "x": 76, "y": 227}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.835Z", "elapsed": 5245, "level": "DEBUG", "message": "Word positioned", "data": {"text": "without", "x": 510, "y": 54}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.836Z", "elapsed": 5246, "level": "DEBUG", "message": "Word positioned", "data": {"text": "copilot", "x": 1017, "y": 180}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.837Z", "elapsed": 5247, "level": "DEBUG", "message": "Word positioned", "data": {"text": "positional", "x": 996, "y": 107}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.838Z", "elapsed": 5248, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 450, "y": 208}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.839Z", "elapsed": 5249, "level": "DEBUG", "message": "Word positioned", "data": {"text": "through", "x": 1060, "y": 196}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.839Z", "elapsed": 5249, "level": "DEBUG", "message": "Word positioned", "data": {"text": "existing", "x": 207, "y": 450}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.840Z", "elapsed": 5250, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.844Z", "elapsed": 5254, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.949Z", "elapsed": 5359, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.950Z", "elapsed": 5360, "level": "DEBUG", "message": "Updating word cloud colors", "data": {"colorScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.952Z", "elapsed": 5362, "level": "INFO", "message": "Word cloud colors updated", "data": {"colorScheme": "bright", "elementsUpdated": 50}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.499Z", "elapsed": 5909, "level": "DEBUG", "message": "Word hovered", "data": {"word": "llm"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.523Z", "elapsed": 5933, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.524Z", "elapsed": 5934, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.528Z", "elapsed": 5938, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.571Z", "elapsed": 5981, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.571Z", "elapsed": 5981, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.572Z", "elapsed": 5982, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.589Z", "elapsed": 5999, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.590Z", "elapsed": 6000, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.591Z", "elapsed": 6001, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.621Z", "elapsed": 6031, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.622Z", "elapsed": 6032, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.622Z", "elapsed": 6032, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.632Z", "elapsed": 6042, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.633Z", "elapsed": 6043, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.634Z", "elapsed": 6044, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.647Z", "elapsed": 6057, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.649Z", "elapsed": 6059, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.650Z", "elapsed": 6060, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.662Z", "elapsed": 6072, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.663Z", "elapsed": 6073, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.663Z", "elapsed": 6073, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.672Z", "elapsed": 6082, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.672Z", "elapsed": 6082, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.673Z", "elapsed": 6083, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.691Z", "elapsed": 6101, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.692Z", "elapsed": 6102, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.694Z", "elapsed": 6104, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.707Z", "elapsed": 6117, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.708Z", "elapsed": 6118, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.711Z", "elapsed": 6121, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.786Z", "elapsed": 6196, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.787Z", "elapsed": 6197, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:41:49.787Z", "elapsed": 6198, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 20, "visibleCount": 20}, "stack": null}], "stats": {"totalLogs": 200, "byLevel": {"DEBUG": 138, "INFO": 60, "WARN": 2, "ERROR": 0}, "errors": [], "warnings": [{"timestamp": "2025-06-05T18:41:44.422Z", "elapsed": 832, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:41:48.515Z", "elapsed": 4925, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 7, "failedTests": 1, "totalErrors": 0, "totalLogs": 201, "screenshots": 3}}