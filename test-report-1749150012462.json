{"timestamp": "2025-06-05T18:59:41.434Z", "tests": [], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:59:42.922Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:59:43.156Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:59:43.235Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:59:43.235Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:59:43.236Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:59:43.237Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:59:43.237Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:59:43.238Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:59:43.238Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:59:43.239Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:59:43.239Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:59:43.239Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:59:43.239Z"}], "screenshots": [], "errors": [{"type": "load-error", "message": "Navigation timeout of 30000 ms exceeded", "timestamp": "2025-06-05T19:00:12.461Z"}, {"type": "test-runner-error", "message": "Failed to load page", "stack": "Error: Failed to load page\n    at WordCloudTester.run (C:\\Users\\<USER>\\Desktop\\test2\\test-headless.js:337:23)", "timestamp": "2025-06-05T19:00:12.461Z"}], "performance": {}, "summary": {"totalTests": 0, "passedTests": 0, "failedTests": 0, "totalErrors": 2, "totalLogs": 13, "screenshots": 0}}