{"timestamp": "2025-06-05T18:23:12.837Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:23:16.537Z"}, {"name": "Loading Element Visible", "passed": false, "error": "Error: failed to find element matching selector \"#loading\"", "timestamp": "2025-06-05T18:23:16.654Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:23:16.661Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:23:16.665Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": false, "error": "No element found for selector: #refresh-btn", "timestamp": "2025-06-05T18:23:26.805Z"}, {"name": "Color Scheme Change", "passed": false, "error": "No element found for selector: #color-scheme", "timestamp": "2025-06-05T18:23:26.822Z"}, {"name": "Search Functionality", "passed": false, "error": "No element found for selector: #paper-search", "timestamp": "2025-06-05T18:23:26.836Z"}, {"name": "Download Logs Button", "passed": false, "timestamp": "2025-06-05T18:23:26.854Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:23:15.214Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:23:15.453Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:23:15.587Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:23:15.598Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:23:15.599Z"}, {"type": "error", "text": "[ERROR] D3 word cloud layout not available JSHandle@object", "timestamp": "2025-06-05T18:23:15.599Z"}, {"type": "error", "text": "[ERROR] Critical error during initialization JSHandle@object", "timestamp": "2025-06-05T18:23:15.600Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749147796400.png", "timestamp": "2025-06-05T18:23:16.520Z"}, {"name": "02-data-load-timeout", "path": "screenshot-02-data-load-timeout-1749147806673.png", "timestamp": "2025-06-05T18:23:26.784Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749147806855.png", "timestamp": "2025-06-05T18:23:26.973Z"}], "errors": [{"type": "page-error", "message": "Cannot read properties of null (reading 'classList')", "stack": "TypeError: Cannot read properties of null (reading 'classList')\n    at eval (:8:32)\n    at <anonymous> (pptr:evaluateHandle;WaitTask.rerun%20(C%3A%5CUsers%5CM%5CDesktop%5Ctest2%5Cnode_modules%5Cpuppeteer-core%5Clib%5Ccjs%5Cpuppeteer%5Ccommon%5CWaitTask.js%3A70%3A54):4:36)\n    at start (pptr:internal:3:3773)\n    at <anonymous> (pptr:evaluate;WaitTask.rerun%20(C%3A%5CUsers%5CM%5CDesktop%5Ctest2%5Cnode_modules%5Cpuppeteer-core%5Clib%5Ccjs%5Cpuppeteer%5Ccommon%5CWaitTask.js%3A100%3A32):2:29)", "timestamp": "2025-06-05T18:23:16.688Z"}], "performance": {"pageLoad": 2728}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:23:15.444Z", "elapsed": 1, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:23:15.444Z"}, "stack": null}, {"timestamp": "2025-06-05T18:23:15.583Z", "elapsed": 140, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:23:15.584Z", "elapsed": 141, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:23:15.585Z", "elapsed": 142, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:23:15.585Z", "elapsed": 142, "level": "ERROR", "message": "D3 word cloud layout not available", "data": {}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:189:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:304:28)"}, {"timestamp": "2025-06-05T18:23:15.586Z", "elapsed": 143, "level": "ERROR", "message": "Critical error during initialization", "data": {"error": "D3 word cloud layout is required but not available", "stack": "Error: D3 word cloud layout is required but not available\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:305:27)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:189:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:784:24)"}], "stats": {"totalLogs": 6, "byLevel": {"DEBUG": 0, "INFO": 4, "WARN": 0, "ERROR": 2}, "errors": [{"timestamp": "2025-06-05T18:23:15.585Z", "elapsed": 142, "level": "ERROR", "message": "D3 word cloud layout not available", "data": {}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:189:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:304:28)"}, {"timestamp": "2025-06-05T18:23:15.586Z", "elapsed": 143, "level": "ERROR", "message": "Critical error during initialization", "data": {"error": "D3 word cloud layout is required but not available", "stack": "Error: D3 word cloud layout is required but not available\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:305:27)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:189:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:784:24)"}], "warnings": []}}, "summary": {"totalTests": 8, "passedTests": 3, "failedTests": 5, "totalErrors": 1, "totalLogs": 7, "screenshots": 3}}