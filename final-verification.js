const puppeteer = require('puppeteer');

class FinalVerificationTest {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = {
            realAPIData: false,
            wordClickHighlight: false,
            wordClickFilter: false,
            wordClickToggle: false,
            highlightToggle: false,
            colorSchemes: false,
            paperLinks: false
        };
    }

    async initialize() {
        console.log('🔍 FINAL VERIFICATION TEST');
        console.log('==========================');

        this.browser = await puppeteer.launch({
            headless: false,
            slowMo: 50,
            args: ['--no-sandbox']
        });

        this.page = await this.browser.newPage();
        await this.page.setViewport({ width: 1280, height: 720 });

        const htmlPath = require('path').resolve(__dirname, 'wordcloud.html');
        await this.page.goto(`file://${htmlPath}`, { waitUntil: 'networkidle0' });

        // Wait for data to load
        await this.page.waitForFunction(() => {
            const loading = document.getElementById('loading');
            return loading && loading.classList.contains('hidden');
        }, { timeout: 15000 });
    }

    async testRealAPIData() {
        console.log('\n1. 🌐 Testing Real API Data...');

        const papersData = await this.page.evaluate(() => {
            const paperCards = document.querySelectorAll('.paper-card');
            const papers = [];

            paperCards.forEach(card => {
                const title = card.querySelector('h3')?.textContent || '';
                const authors = card.querySelector('.text-sm.text-gray-500')?.textContent || '';
                const dateText = card.querySelector('.text-xs.bg-indigo-100')?.textContent || '';
                const link = card.querySelector('a[href]')?.href || '';

                papers.push({ title, authors, dateText, link });
            });

            return papers;
        });

        // Check for 2024 dates (real API data)
        const has2024Papers = papersData.some(paper =>
            paper.dateText.includes('2024') ||
            paper.title.includes('2024') ||
            paper.authors.includes('2024')
        );

        // Check for real Hugging Face URLs
        const hasRealLinks = papersData.every(paper =>
            paper.link.includes('huggingface.co/papers/') && !paper.link.includes('#')
        );

        // Check for real authors (not "Unknown Authors")
        const hasRealAuthors = papersData.every(paper =>
            !paper.authors.includes('Unknown Authors') &&
            paper.authors.trim() !== '' &&
            paper.authors !== 'By '
        );

        this.results.realAPIData = has2024Papers && hasRealLinks && hasRealAuthors;

        console.log(`   📅 Has 2024 papers: ${has2024Papers ? '✅' : '❌'}`);
        console.log(`   🔗 Real HF links: ${hasRealLinks ? '✅' : '❌'}`);
        console.log(`   👥 Real authors: ${hasRealAuthors ? '✅' : '❌'}`);
        console.log(`   📊 Sample paper:`, papersData[0]);
        console.log(`   🎯 REAL API DATA: ${this.results.realAPIData ? '✅ PASS' : '❌ FAIL'}`);
    }

    async testWordClickFunctionality() {
        console.log('\n2. 🖱️ Testing Word Click Functionality...');

        // Get initial paper count
        const initialCount = await this.page.evaluate(() => {
            return document.querySelectorAll('.paper-card:not([style*="display: none"])').length;
        });

        // Click on a word
        await this.page.click('.word');
        await this.page.waitForTimeout(500);

        // Check if word is highlighted
        const wordHighlighted = await this.page.evaluate(() => {
            return document.querySelector('.word.selected') !== null;
        });

        // Check if papers are filtered
        const filteredCount = await this.page.evaluate(() => {
            return document.querySelectorAll('.paper-card:not([style*="display: none"])').length;
        });

        // Check if search input was updated
        const searchValue = await this.page.$eval('#paper-search', el => el.value);

        this.results.wordClickHighlight = wordHighlighted;
        this.results.wordClickFilter = filteredCount < initialCount && searchValue !== '';

        console.log(`   🎯 Word highlighted: ${wordHighlighted ? '✅' : '❌'}`);
        console.log(`   🔍 Papers filtered: ${this.results.wordClickFilter ? '✅' : '❌'} (${initialCount} → ${filteredCount})`);
        console.log(`   📝 Search updated: ${searchValue ? '✅' : '❌'} ("${searchValue}")`);

        // Test toggle (click same word again)
        await this.page.click('.word.selected');
        await this.page.waitForTimeout(500);

        const wordUnselected = await this.page.evaluate(() => {
            return document.querySelector('.word.selected') === null;
        });

        const allPapersShown = await this.page.evaluate(() => {
            return document.querySelectorAll('.paper-card:not([style*="display: none"])').length;
        });

        this.results.wordClickToggle = wordUnselected && allPapersShown === initialCount;

        console.log(`   🔄 Word toggle: ${this.results.wordClickToggle ? '✅' : '❌'} (${allPapersShown} papers shown)`);
    }

    async testHighlightToggle() {
        console.log('\n3. 🎨 Testing Highlight Toggle...');

        const initialState = await this.page.$eval('#highlight-toggle', el => el.checked);

        // Toggle highlight mode
        await this.page.click('label[for="highlight-toggle"]');
        await this.page.waitForTimeout(300);

        const newState = await this.page.$eval('#highlight-toggle', el => el.checked);
        const hasHighlightClass = await this.page.evaluate(() => {
            return document.getElementById('word-cloud').classList.contains('highlight-mode');
        });

        this.results.highlightToggle = (initialState !== newState) && (hasHighlightClass === newState);

        console.log(`   🔄 Toggle changed: ${initialState !== newState ? '✅' : '❌'} (${initialState} → ${newState})`);
        console.log(`   🎨 CSS class updated: ${hasHighlightClass === newState ? '✅' : '❌'}`);
        console.log(`   🎯 HIGHLIGHT TOGGLE: ${this.results.highlightToggle ? '✅ PASS' : '❌ FAIL'}`);
    }

    async testColorSchemes() {
        console.log('\n4. 🌈 Testing Color Schemes...');

        const schemes = ['bright', 'pastel', 'monochrome', 'default'];
        let allWorking = true;

        for (const scheme of schemes) {
            await this.page.select('#color-scheme', scheme);
            await this.page.waitForTimeout(300);

            const colors = await this.page.evaluate(() => {
                const words = document.querySelectorAll('.word');
                return Array.from(words).slice(0, 3).map(word =>
                    window.getComputedStyle(word).color
                );
            });

            const hasColors = colors.every(color => color && color !== 'rgba(0, 0, 0, 0)');
            console.log(`   ${scheme}: ${hasColors ? '✅' : '❌'} (${colors[0]})`);

            if (!hasColors) allWorking = false;
        }

        this.results.colorSchemes = allWorking;
        console.log(`   🎯 COLOR SCHEMES: ${this.results.colorSchemes ? '✅ PASS' : '❌ FAIL'}`);
    }

    async testPaperLinks() {
        console.log('\n5. 🔗 Testing Paper Links...');

        const links = await this.page.evaluate(() => {
            const paperLinks = document.querySelectorAll('.paper-card a[href]');
            return Array.from(paperLinks).map(link => link.href);
        });

        const allValidLinks = links.every(link =>
            link.includes('huggingface.co/papers/') &&
            !link.includes('#') &&
            link.match(/\/papers\/\d+\.\d+\.\d+/)
        );

        this.results.paperLinks = allValidLinks && links.length > 0;

        console.log(`   📊 Total links: ${links.length}`);
        console.log(`   🔗 Valid format: ${allValidLinks ? '✅' : '❌'}`);
        console.log(`   📝 Sample: ${links[0]}`);
        console.log(`   🎯 PAPER LINKS: ${this.results.paperLinks ? '✅ PASS' : '❌ FAIL'}`);
    }

    async generateFinalReport() {
        console.log('\n📊 FINAL VERIFICATION RESULTS');
        console.log('==============================');

        const tests = [
            { name: 'Real API Data', result: this.results.realAPIData },
            { name: 'Word Click Highlight', result: this.results.wordClickHighlight },
            { name: 'Word Click Filter', result: this.results.wordClickFilter },
            { name: 'Word Click Toggle', result: this.results.wordClickToggle },
            { name: 'Highlight Toggle', result: this.results.highlightToggle },
            { name: 'Color Schemes', result: this.results.colorSchemes },
            { name: 'Paper Links', result: this.results.paperLinks }
        ];

        const passedTests = tests.filter(test => test.result).length;
        const totalTests = tests.length;

        tests.forEach(test => {
            console.log(`${test.result ? '✅' : '❌'} ${test.name}`);
        });

        console.log(`\n📈 SCORE: ${passedTests}/${totalTests} (${Math.round(passedTests / totalTests * 100)}%)`);

        if (passedTests === totalTests) {
            console.log('🎉 ALL TESTS PASSED! Application is professional quality!');
            return true;
        } else {
            console.log('💥 Some tests failed. Application needs fixes.');
            return false;
        }
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.testRealAPIData();
            await this.testWordClickFunctionality();
            await this.testHighlightToggle();
            await this.testColorSchemes();
            await this.testPaperLinks();

            const success = await this.generateFinalReport();
            return success;

        } catch (error) {
            console.error('💥 Test failed:', error.message);
            return false;
        } finally {
            await this.cleanup();
        }
    }
}

// Run the final verification
if (require.main === module) {
    const tester = new FinalVerificationTest();
    tester.run()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('Test runner failed:', error);
            process.exit(1);
        });
}

module.exports = FinalVerificationTest;
