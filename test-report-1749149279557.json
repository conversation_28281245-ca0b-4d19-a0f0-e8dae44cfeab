{"timestamp": "2025-06-05T18:47:48.747Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:47:54.013Z"}, {"name": "Loading Element Visible", "passed": false, "timestamp": "2025-06-05T18:47:54.258Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:47:54.264Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:47:54.272Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:47:56.340Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:47:57.263Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:47:58.797Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:47:59.076Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:47:51.032Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:47:51.318Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:47:51.429Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:47:51.430Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:47:51.432Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:47:51.432Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:47:51.433Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:47:51.433Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:47:51.433Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:47:51.434Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:47:51.434Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:47:51.435Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:47:51.435Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:47:52.315Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:47:52.319Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:47:52.327Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:47:52.328Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:47:52.331Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:52.331Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:52.331Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:47:52.332Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:47:52.332Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:47:52.332Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:47:52.332Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:47:52.332Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:47:52.346Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:47:52.349Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:47:52.350Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:47:52.350Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:47:52.350Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:47:52.355Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:47:52.426Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:47:52.427Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:52.428Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:47:52.428Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.600Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.615Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.630Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.631Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.631Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.631Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.638Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.645Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.649Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.652Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.658Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.659Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.659Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.659Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.660Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.660Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.661Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.662Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.662Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.662Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.662Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.663Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.663Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.663Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.665Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.665Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.665Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.666Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.666Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.666Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.666Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.667Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.667Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.668Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.669Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.669Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.669Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.674Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.674Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:52.675Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:47:52.675Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:47:52.676Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:47:55.316Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:47:55.316Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:47:55.316Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:47:55.316Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:47:55.317Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:47:56.142Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:47:56.143Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:47:56.144Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:47:56.144Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:47:56.144Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:56.145Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:56.146Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:47:56.151Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:47:56.165Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:47:56.165Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:47:56.165Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:47:56.166Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:47:56.172Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:47:56.174Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:47:56.175Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:47:56.175Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:47:56.176Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:47:56.176Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:47:56.276Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:47:56.278Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:56.278Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:47:56.278Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.512Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.513Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.533Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.533Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.534Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.534Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.535Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.535Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.536Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.548Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.548Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.549Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.549Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.549Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.549Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.549Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.550Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.550Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.550Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.551Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.551Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.552Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.567Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.567Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.568Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.568Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.568Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.569Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.581Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.582Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.583Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.583Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.584Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.584Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.584Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.584Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.585Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.585Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.585Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.592Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.592Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.592Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.593Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.593Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.593Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.593Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.593Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.594Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.594Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:47:56.594Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:47:56.594Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:47:56.595Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:47:56.647Z"}, {"type": "log", "text": "[DEBUG] Updating word cloud colors JSHandle@object", "timestamp": "2025-06-05T18:47:56.648Z"}, {"type": "log", "text": "[INFO] Word cloud colors updated JSHandle@object", "timestamp": "2025-06-05T18:47:56.650Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:57.931Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:57.933Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:57.933Z"}, {"type": "log", "text": "[DEBUG] Word hovered JSHandle@object", "timestamp": "2025-06-05T18:47:57.938Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.028Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.029Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.030Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.047Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.052Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.059Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.079Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.079Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.080Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.152Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.153Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.158Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.164Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.166Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.167Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.181Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.193Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.194Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.198Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.200Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.200Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.247Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.248Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.249Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.261Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.263Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.264Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:47:58.280Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:47:58.280Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:47:58.281Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749149273267.png", "timestamp": "2025-06-05T18:47:53.995Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749149274377.png", "timestamp": "2025-06-05T18:47:55.149Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749149279077.png", "timestamp": "2025-06-05T18:47:59.484Z"}], "errors": [], "performance": {"pageLoad": 3720}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:47:51.316Z", "elapsed": 1, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:47:51.315Z"}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.412Z", "elapsed": 97, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.414Z", "elapsed": 99, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.415Z", "elapsed": 100, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.415Z", "elapsed": 100, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.415Z", "elapsed": 100, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.416Z", "elapsed": 101, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.417Z", "elapsed": 102, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.417Z", "elapsed": 102, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.417Z", "elapsed": 102, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.421Z", "elapsed": 106, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:51.423Z", "elapsed": 108, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.250Z", "elapsed": 935, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 615424, "dataType": "object", "isArray": true, "searchQuery": "attention"}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.254Z", "elapsed": 939, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 117, "processedCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.255Z", "elapsed": 940, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 839}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.255Z", "elapsed": 940, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.256Z", "elapsed": 941, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.257Z", "elapsed": 942, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 19743}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.258Z", "elapsed": 943, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 2770}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.260Z", "elapsed": 945, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 932, "topWords": 50, "mostFrequent": "attention"}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.261Z", "elapsed": 946, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 5}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.261Z", "elapsed": 946, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 15, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.262Z", "elapsed": 947, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.262Z", "elapsed": 947, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.263Z", "elapsed": 948, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.263Z", "elapsed": 948, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.264Z", "elapsed": 949, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.266Z", "elapsed": 951, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.266Z", "elapsed": 951, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 4}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.267Z", "elapsed": 952, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 851}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.383Z", "elapsed": 1068, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.383Z", "elapsed": 1068, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.384Z", "elapsed": 1069, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.384Z", "elapsed": 1069, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.599Z", "elapsed": 1284, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 686, "y": 241}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.601Z", "elapsed": 1287, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 551, "y": 215}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.606Z", "elapsed": 1291, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 408, "y": 326}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.608Z", "elapsed": 1293, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llms", "x": 845, "y": 328}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.613Z", "elapsed": 1298, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 792, "y": 154}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.614Z", "elapsed": 1299, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 761, "y": 287}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.615Z", "elapsed": 1300, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 589, "y": 129}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.617Z", "elapsed": 1302, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 514, "y": 123}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.618Z", "elapsed": 1303, "level": "DEBUG", "message": "Word positioned", "data": {"text": "heads", "x": 719, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.622Z", "elapsed": 1307, "level": "DEBUG", "message": "Word positioned", "data": {"text": "their", "x": 716, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.623Z", "elapsed": 1308, "level": "DEBUG", "message": "Word positioned", "data": {"text": "show", "x": 655, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.623Z", "elapsed": 1308, "level": "DEBUG", "message": "Word positioned", "data": {"text": "input", "x": 586, "y": 336}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.624Z", "elapsed": 1309, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 864, "y": 187}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.625Z", "elapsed": 1310, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 348, "y": 261}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.626Z", "elapsed": 1311, "level": "DEBUG", "message": "Word positioned", "data": {"text": "network", "x": 453, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.627Z", "elapsed": 1312, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformers", "x": 893, "y": 368}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.629Z", "elapsed": 1314, "level": "DEBUG", "message": "Word positioned", "data": {"text": "inference", "x": 252, "y": 186}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.630Z", "elapsed": 1315, "level": "DEBUG", "message": "Word positioned", "data": {"text": "performance", "x": 923, "y": 119}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.631Z", "elapsed": 1316, "level": "DEBUG", "message": "Word positioned", "data": {"text": "however", "x": 1015, "y": 369}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.632Z", "elapsed": 1317, "level": "DEBUG", "message": "Word positioned", "data": {"text": "biases", "x": 998, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.633Z", "elapsed": 1318, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mechanisms", "x": 209, "y": 245}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.634Z", "elapsed": 1319, "level": "DEBUG", "message": "Word positioned", "data": {"text": "new", "x": 659, "y": 383}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.635Z", "elapsed": 1320, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 500, "y": 365}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.635Z", "elapsed": 1320, "level": "DEBUG", "message": "Word positioned", "data": {"text": "more", "x": 806, "y": 346}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.639Z", "elapsed": 1324, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 411, "y": 123}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.641Z", "elapsed": 1326, "level": "DEBUG", "message": "Word positioned", "data": {"text": "memory", "x": 261, "y": 350}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.642Z", "elapsed": 1327, "level": "DEBUG", "message": "Word positioned", "data": {"text": "rnn", "x": 356, "y": 150}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.642Z", "elapsed": 1327, "level": "DEBUG", "message": "Word positioned", "data": {"text": "image", "x": 369, "y": 210}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.643Z", "elapsed": 1328, "level": "DEBUG", "message": "Word positioned", "data": {"text": "methods", "x": 167, "y": 283}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.644Z", "elapsed": 1329, "level": "DEBUG", "message": "Word positioned", "data": {"text": "also", "x": 300, "y": 119}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.645Z", "elapsed": 1330, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 359, "y": 101}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.646Z", "elapsed": 1331, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficiency", "x": 362, "y": 404}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.646Z", "elapsed": 1331, "level": "DEBUG", "message": "Word positioned", "data": {"text": "sparsity", "x": 817, "y": 413}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.647Z", "elapsed": 1332, "level": "DEBUG", "message": "Word positioned", "data": {"text": "length", "x": 719, "y": 139}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.648Z", "elapsed": 1333, "level": "DEBUG", "message": "Word positioned", "data": {"text": "cottention", "x": 114, "y": 208}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.649Z", "elapsed": 1334, "level": "DEBUG", "message": "Word positioned", "data": {"text": "during", "x": 572, "y": 399}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.650Z", "elapsed": 1335, "level": "DEBUG", "message": "Word positioned", "data": {"text": "they", "x": 502, "y": 300}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.651Z", "elapsed": 1336, "level": "DEBUG", "message": "Word positioned", "data": {"text": "dataset", "x": 960, "y": 292}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.652Z", "elapsed": 1337, "level": "DEBUG", "message": "Word positioned", "data": {"text": "prompting", "x": 685, "y": 64}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.656Z", "elapsed": 1341, "level": "DEBUG", "message": "Word positioned", "data": {"text": "internal", "x": 831, "y": 53}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.658Z", "elapsed": 1343, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 428, "y": 415}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.659Z", "elapsed": 1344, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 186, "y": 365}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.661Z", "elapsed": 1346, "level": "DEBUG", "message": "Word positioned", "data": {"text": "time", "x": 475, "y": 89}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.661Z", "elapsed": 1346, "level": "DEBUG", "message": "Word positioned", "data": {"text": "over", "x": 833, "y": 222}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.662Z", "elapsed": 1347, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 701, "y": 441}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.663Z", "elapsed": 1348, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficient", "x": 964, "y": 163}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.664Z", "elapsed": 1349, "level": "DEBUG", "message": "Word positioned", "data": {"text": "not", "x": 604, "y": 268}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.664Z", "elapsed": 1350, "level": "DEBUG", "message": "Word positioned", "data": {"text": "achieve", "x": 175, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.665Z", "elapsed": 1350, "level": "DEBUG", "message": "Word positioned", "data": {"text": "softmax", "x": 1038, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.666Z", "elapsed": 1351, "level": "DEBUG", "message": "Word positioned", "data": {"text": "sequences", "x": 137, "y": 277}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.667Z", "elapsed": 1352, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:52.675Z", "elapsed": 1360, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:55.300Z", "elapsed": 3985, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:55.300Z", "elapsed": 3986, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:55.301Z", "elapsed": 3986, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:55.301Z", "elapsed": 3986, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:55.302Z", "elapsed": 3987, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.141Z", "elapsed": 4826, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 663008, "dataType": "object", "isArray": true, "searchQuery": "AI"}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.143Z", "elapsed": 4828, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 111, "processedCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.143Z", "elapsed": 4828, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 843}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.143Z", "elapsed": 4828, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.144Z", "elapsed": 4829, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.144Z", "elapsed": 4829, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 21903}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.145Z", "elapsed": 4830, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3059}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.149Z", "elapsed": 4834, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1131, "topWords": 50, "mostFrequent": "language"}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.150Z", "elapsed": 4835, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 6}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.151Z", "elapsed": 4836, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 15, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.151Z", "elapsed": 4836, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.152Z", "elapsed": 4837, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.158Z", "elapsed": 4843, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.159Z", "elapsed": 4844, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.161Z", "elapsed": 4846, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.165Z", "elapsed": 4850, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.166Z", "elapsed": 4851, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 14}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.167Z", "elapsed": 4852, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 866}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.274Z", "elapsed": 4959, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.275Z", "elapsed": 4960, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.276Z", "elapsed": 4961, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.276Z", "elapsed": 4961, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.511Z", "elapsed": 5196, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 602, "y": 340}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.512Z", "elapsed": 5197, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 582, "y": 200}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.513Z", "elapsed": 5198, "level": "DEBUG", "message": "Word positioned", "data": {"text": "systems", "x": 477, "y": 363}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.516Z", "elapsed": 5201, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agents", "x": 556, "y": 102}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.516Z", "elapsed": 5201, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 713, "y": 152}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.517Z", "elapsed": 5202, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 687, "y": 227}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.517Z", "elapsed": 5202, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 824, "y": 346}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.517Z", "elapsed": 5202, "level": "DEBUG", "message": "Word positioned", "data": {"text": "research", "x": 713, "y": 331}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.518Z", "elapsed": 5203, "level": "DEBUG", "message": "Word positioned", "data": {"text": "system", "x": 524, "y": 351}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.528Z", "elapsed": 5213, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agent", "x": 486, "y": 224}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.529Z", "elapsed": 5214, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 842, "y": 250}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.544Z", "elapsed": 5229, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agentic", "x": 814, "y": 200}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.545Z", "elapsed": 5230, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 366, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.545Z", "elapsed": 5230, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llms", "x": 606, "y": 114}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.546Z", "elapsed": 5231, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 343, "y": 299}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.547Z", "elapsed": 5232, "level": "DEBUG", "message": "Word positioned", "data": {"text": "engineering", "x": 427, "y": 146}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.547Z", "elapsed": 5232, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 286, "y": 227}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.548Z", "elapsed": 5233, "level": "DEBUG", "message": "Word positioned", "data": {"text": "human", "x": 931, "y": 306}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.549Z", "elapsed": 5234, "level": "DEBUG", "message": "Word positioned", "data": {"text": "benchmarks", "x": 769, "y": 372}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.549Z", "elapsed": 5234, "level": "DEBUG", "message": "Word positioned", "data": {"text": "their", "x": 481, "y": 123}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.550Z", "elapsed": 5235, "level": "DEBUG", "message": "Word positioned", "data": {"text": "memory", "x": 430, "y": 343}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.551Z", "elapsed": 5236, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 238, "y": 226}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.552Z", "elapsed": 5237, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 123, "y": 249}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.557Z", "elapsed": 5242, "level": "DEBUG", "message": "Word positioned", "data": {"text": "suggestions", "x": 985, "y": 347}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.558Z", "elapsed": 5243, "level": "DEBUG", "message": "Word positioned", "data": {"text": "code", "x": 180, "y": 204}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.559Z", "elapsed": 5244, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 584, "y": 242}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.561Z", "elapsed": 5246, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scientific", "x": 908, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.561Z", "elapsed": 5246, "level": "DEBUG", "message": "Word positioned", "data": {"text": "second", "x": 649, "y": 360}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.562Z", "elapsed": 5247, "level": "DEBUG", "message": "Word positioned", "data": {"text": "through", "x": 345, "y": 418}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.563Z", "elapsed": 5248, "level": "DEBUG", "message": "Word positioned", "data": {"text": "responses", "x": 238, "y": 360}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.564Z", "elapsed": 5249, "level": "DEBUG", "message": "Word positioned", "data": {"text": "evaluation", "x": 130, "y": 325}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.565Z", "elapsed": 5250, "level": "DEBUG", "message": "Word positioned", "data": {"text": "capabilities", "x": 961, "y": 193}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.565Z", "elapsed": 5250, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 1073, "y": 261}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.566Z", "elapsed": 5251, "level": "DEBUG", "message": "Word positioned", "data": {"text": "github", "x": 915, "y": 399}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.567Z", "elapsed": 5252, "level": "DEBUG", "message": "Word positioned", "data": {"text": "music", "x": 372, "y": 136}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.568Z", "elapsed": 5253, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tools", "x": 358, "y": 78}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.573Z", "elapsed": 5258, "level": "DEBUG", "message": "Word positioned", "data": {"text": "solutions", "x": 772, "y": 103}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.574Z", "elapsed": 5259, "level": "DEBUG", "message": "Word positioned", "data": {"text": "user", "x": 564, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.575Z", "elapsed": 5260, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduce", "x": 882, "y": 94}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.576Z", "elapsed": 5261, "level": "DEBUG", "message": "Word positioned", "data": {"text": "first", "x": 181, "y": 161}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.577Z", "elapsed": 5262, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 714, "y": 442}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.578Z", "elapsed": 5263, "level": "DEBUG", "message": "Word positioned", "data": {"text": "interaction", "x": 215, "y": 104}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.580Z", "elapsed": 5265, "level": "DEBUG", "message": "Word positioned", "data": {"text": "provide", "x": 85, "y": 168}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.581Z", "elapsed": 5266, "level": "DEBUG", "message": "Word positioned", "data": {"text": "software", "x": 589, "y": 456}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.582Z", "elapsed": 5267, "level": "DEBUG", "message": "Word positioned", "data": {"text": "questions", "x": 252, "y": 426}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.583Z", "elapsed": 5268, "level": "DEBUG", "message": "Word positioned", "data": {"text": "users", "x": 709, "y": 82}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.584Z", "elapsed": 5269, "level": "DEBUG", "message": "Word positioned", "data": {"text": "knowledge", "x": 403, "y": 462}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.585Z", "elapsed": 5270, "level": "DEBUG", "message": "Word positioned", "data": {"text": "been", "x": 379, "y": 261}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.590Z", "elapsed": 5275, "level": "DEBUG", "message": "Word positioned", "data": {"text": "task", "x": 850, "y": 150}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.591Z", "elapsed": 5276, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 742, "y": 83}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.592Z", "elapsed": 5277, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.594Z", "elapsed": 5279, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.646Z", "elapsed": 5331, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.647Z", "elapsed": 5332, "level": "DEBUG", "message": "Updating word cloud colors", "data": {"colorScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.648Z", "elapsed": 5333, "level": "INFO", "message": "Word cloud colors updated", "data": {"colorScheme": "bright", "elementsUpdated": 50}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.929Z", "elapsed": 6614, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.930Z", "elapsed": 6615, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.931Z", "elapsed": 6616, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 15, "visibleCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.934Z", "elapsed": 6619, "level": "DEBUG", "message": "Word hovered", "data": {"word": "systems"}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.990Z", "elapsed": 6675, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.991Z", "elapsed": 6676, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:47:57.993Z", "elapsed": 6678, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 15, "visibleCount": 14}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.035Z", "elapsed": 6720, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.037Z", "elapsed": 6722, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.039Z", "elapsed": 6724, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 15, "visibleCount": 11}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.066Z", "elapsed": 6751, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.067Z", "elapsed": 6752, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.067Z", "elapsed": 6752, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.149Z", "elapsed": 6834, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.149Z", "elapsed": 6834, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.150Z", "elapsed": 6835, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.163Z", "elapsed": 6848, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.163Z", "elapsed": 6848, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.164Z", "elapsed": 6849, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.178Z", "elapsed": 6863, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.179Z", "elapsed": 6864, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.180Z", "elapsed": 6865, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.197Z", "elapsed": 6882, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.197Z", "elapsed": 6882, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.198Z", "elapsed": 6883, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.231Z", "elapsed": 6916, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.232Z", "elapsed": 6917, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.233Z", "elapsed": 6918, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.258Z", "elapsed": 6943, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.259Z", "elapsed": 6944, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.261Z", "elapsed": 6946, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 15, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.279Z", "elapsed": 6964, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.279Z", "elapsed": 6964, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:47:58.280Z", "elapsed": 6965, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 15, "visibleCount": 0}, "stack": null}], "stats": {"totalLogs": 202, "byLevel": {"DEBUG": 140, "INFO": 60, "WARN": 2, "ERROR": 0}, "errors": [], "warnings": [{"timestamp": "2025-06-05T18:47:52.263Z", "elapsed": 948, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:47:56.159Z", "elapsed": 4844, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 7, "failedTests": 1, "totalErrors": 0, "totalLogs": 203, "screenshots": 3}}