<?xml version="1.0" encoding="UTF-8"?>
<?mso-application progid="Excel.Sheet"?><Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:c="urn:schemas-microsoft-com:office:component:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:x2="http://schemas.microsoft.com/office/excel/2003/xml" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"><OfficeDocumentSettings xmlns="urn:schemas-microsoft-com:office:office"><Colors><Color><Index>3</Index><RGB>#000000</RGB></Color><Color><Index>4</Index><RGB>#0000ee</RGB></Color><Color><Index>5</Index><RGB>#006600</RGB></Color><Color><Index>6</Index><RGB>#333333</RGB></Color><Color><Index>7</Index><RGB>#808080</RGB></Color><Color><Index>8</Index><RGB>#996600</RGB></Color><Color><Index>9</Index><RGB>#c0c0c0</RGB></Color><Color><Index>10</Index><RGB>#cc0000</RGB></Color><Color><Index>11</Index><RGB>#ccffcc</RGB></Color><Color><Index>12</Index><RGB>#dddddd</RGB></Color><Color><Index>13</Index><RGB>#ffcccc</RGB></Color><Color><Index>14</Index><RGB>#ffffcc</RGB></Color><Color><Index>15</Index><RGB>#ffffff</RGB></Color></Colors></OfficeDocumentSettings><ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel"><WindowHeight>9000</WindowHeight><WindowWidth>13860</WindowWidth><WindowTopX>240</WindowTopX><WindowTopY>75</WindowTopY><ProtectStructure>False</ProtectStructure><ProtectWindows>False</ProtectWindows></ExcelWorkbook><Styles><Style ss:ID="Default" ss:Name="Default"/><Style ss:ID="Note" ss:Name="Note"><Font ss:FontName="Liberation Sans" ss:Size="10"/></Style><Style ss:ID="Default" ss:Name="Default"/><Style ss:ID="Heading" ss:Name="Heading"><Alignment/><Font ss:Bold="1" ss:Color="#000000" ss:Size="24"/></Style><Style ss:ID="Heading_20_1" ss:Name="Heading 1"><Alignment/><Font ss:Bold="1" ss:Color="#000000" ss:Size="18"/></Style><Style ss:ID="Heading_20_2" ss:Name="Heading 2"><Alignment/><Font ss:Bold="1" ss:Color="#000000" ss:Size="12"/></Style><Style ss:ID="Text" ss:Name="Text"><Alignment/></Style><Style ss:ID="Note" ss:Name="Note"><Alignment/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#808080"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#808080"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#808080"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1" ss:Color="#808080"/></Borders><Interior ss:Color="#ffffcc" ss:Pattern="Solid"/></Style><Style ss:ID="Footnote" ss:Name="Footnote"><Alignment/></Style><Style ss:ID="Hyperlink" ss:Name="Hyperlink"><Alignment/></Style><Style ss:ID="Status" ss:Name="Status"><Alignment/></Style><Style ss:ID="Good" ss:Name="Good"><Alignment/><Interior ss:Color="#ccffcc" ss:Pattern="Solid"/></Style><Style ss:ID="Neutral" ss:Name="Neutral"><Alignment/><Interior ss:Color="#ffffcc" ss:Pattern="Solid"/></Style><Style ss:ID="Bad" ss:Name="Bad"><Alignment/><Interior ss:Color="#ffcccc" ss:Pattern="Solid"/></Style><Style ss:ID="Warning" ss:Name="Warning"><Alignment/></Style><Style ss:ID="Error" ss:Name="Error"><Alignment/><Interior ss:Color="#cc0000" ss:Pattern="Solid"/></Style><Style ss:ID="Accent" ss:Name="Accent"><Alignment/></Style><Style ss:ID="Accent_20_1" ss:Name="Accent 1"><Alignment/><Font ss:Bold="1" ss:Color="#ffffff"/><Interior ss:Color="#000000" ss:Pattern="Solid"/></Style><Style ss:ID="Accent_20_2" ss:Name="Accent 2"><Alignment/><Font ss:Bold="1" ss:Color="#ffffff"/><Interior ss:Color="#808080" ss:Pattern="Solid"/></Style><Style ss:ID="Accent_20_3" ss:Name="Accent 3"><Alignment/><Interior ss:Color="#dddddd" ss:Pattern="Solid"/></Style><Style ss:ID="Result" ss:Name="Result"><Alignment/><Font ss:Bold="1" ss:Italic="1" ss:Underline="Single"/></Style><Style ss:ID="co1"/><Style ss:ID="co2"/><Style ss:ID="co3"/><Style ss:ID="co4"/><Style ss:ID="co5"/><Style ss:ID="co6"/><Style ss:ID="co7"/><Style ss:ID="co8"/><Style ss:ID="ta1"/><Style ss:ID="ce1"><NumberFormat ss:Format="General"/></Style></Styles><ss:Worksheet ss:Name="financial_data"><Table ss:StyleID="ta1"><Column ss:Width="81.2952"/><Column ss:Width="48.8376"/><Column ss:Width="52.6968"/><Column ss:Width="49.6656"/><Column ss:Width="46.548"/><Column ss:Span="1" ss:Width="51.192"/><Column ss:Index="8" ss:Width="49.6656"/><Column ss:Span="1" ss:Width="51.192"/><Column ss:Index="11" ss:Width="49.6656"/><Column ss:Span="1" ss:Width="50.4"/><Row ss:Height="12.816"><Cell ss:Index="2" ss:StyleID="ce1"><Data ss:Type="String">Apr 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">May 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Jun 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Jul 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Aug 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Sep 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Oct 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Nov 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Dec 2024</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Jan 2025</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Feb 2025</Data></Cell><Cell ss:StyleID="ce1"><Data ss:Type="String">Mar 2025</Data></Cell></Row><Row ss:Height="12.816"><Cell><Data ss:Type="String">carte bleu</Data></Cell><Cell><Data ss:Type="Number">24487</Data></Cell><Cell><Data ss:Type="Number">25528.5</Data></Cell><Cell><Data ss:Type="Number">18408.44</Data></Cell><Cell><Data ss:Type="Number">16163</Data></Cell><Cell><Data ss:Type="Number">25416.96</Data></Cell><Cell><Data ss:Type="Number">19549</Data></Cell><Cell><Data ss:Type="Number">22598.5</Data></Cell><Cell><Data ss:Type="Number">20850.1</Data></Cell><Cell><Data ss:Type="Number">33278</Data></Cell><Cell><Data ss:Type="Number">18377.5</Data></Cell><Cell><Data ss:Type="Number">25905.25</Data></Cell><Cell><Data ss:Type="Number">37693.03</Data></Cell></Row><Row ss:Height="12.816"><Cell><Data ss:Type="String">american express</Data></Cell><Cell><Data ss:Type="Number">413.5</Data></Cell><Cell><Data ss:Type="Number">170</Data></Cell><Cell><Data ss:Type="Number">278.25</Data></Cell><Cell><Data ss:Type="Number">686.5</Data></Cell><Cell><Data ss:Type="Number">769.5</Data></Cell><Cell><Data ss:Type="Number">763</Data></Cell><Cell><Data ss:Type="Number">134</Data></Cell><Cell><Data ss:Type="Number">314</Data></Cell><Cell><Data ss:Type="Number">78.5</Data></Cell><Cell><Data ss:Type="Number">267</Data></Cell><Cell><Data ss:Type="Number">380.5</Data></Cell><Cell><Data ss:Type="Number">229</Data></Cell></Row><Row ss:Height="12.816"><Cell><Data ss:Type="String">ticket restaurant</Data></Cell><Cell><Data ss:Type="Number">48.5</Data></Cell><Cell><Data ss:Type="Number">373.24</Data></Cell><Cell><Data ss:Type="Number">395.31</Data></Cell><Cell><Data ss:Type="Number">432.4</Data></Cell><Cell><Data ss:Type="Number">502.94</Data></Cell><Cell><Data ss:Type="Number">333.4</Data></Cell><Cell><Data ss:Type="Number">353.85</Data></Cell><Cell><Data ss:Type="Number">168.5</Data></Cell><Cell><Data ss:Type="Number">206.35</Data></Cell><Cell><Data ss:Type="Number">282.7</Data></Cell><Cell><Data ss:Type="Number">20325</Data></Cell><Cell><Data ss:Type="Number">204.27</Data></Cell></Row><Row ss:Height="12.816"><Cell><Data ss:Type="String">espece</Data></Cell><Cell><Data ss:Type="Number">1182.5</Data></Cell><Cell><Data ss:Type="Number">1480.26</Data></Cell><Cell><Data ss:Type="Number">955</Data></Cell><Cell><Data ss:Type="Number">1061.1</Data></Cell><Cell><Data ss:Type="Number">922.1</Data></Cell><Cell><Data ss:Type="Number">781.6</Data></Cell><Cell><Data ss:Type="Number">819.15</Data></Cell><Cell><Data ss:Type="Number">887.9</Data></Cell><Cell><Data ss:Type="Number">911.65</Data></Cell><Cell><Data ss:Type="Number">1091.8</Data></Cell><Cell><Data ss:Type="Number">1072.25</Data></Cell><Cell><Data ss:Type="Number">1121.7</Data></Cell></Row><Row ss:Height="12.816"><Cell><Data ss:Type="String">Total mois</Data></Cell><Cell ss:Formula="of:=SUM([.B2:.B5])"><Data ss:Type="Number">26131.5</Data></Cell><Cell ss:Formula="of:=SUM([.C2:.C5])"><Data ss:Type="Number">27552</Data></Cell><Cell ss:Formula="of:=SUM([.D2:.D5])"><Data ss:Type="Number">20037</Data></Cell><Cell ss:Formula="of:=SUM([.E2:.E5])"><Data ss:Type="Number">18343</Data></Cell><Cell ss:Formula="of:=SUM([.F2:.F5])"><Data ss:Type="Number">27611.5</Data></Cell><Cell ss:Formula="of:=SUM([.G2:.G5])"><Data ss:Type="Number">21427</Data></Cell><Cell ss:Formula="of:=SUM([.H2:.H5])"><Data ss:Type="Number">23905.5</Data></Cell><Cell ss:Formula="of:=SUM([.I2:.I5])"><Data ss:Type="Number">22220.5</Data></Cell><Cell ss:Formula="of:=SUM([.J2:.J5])"><Data ss:Type="Number">34474.5</Data></Cell><Cell ss:Formula="of:=SUM([.K2:.K5])"><Data ss:Type="Number">20019</Data></Cell><Cell ss:Formula="of:=SUM([.L2:.L5])"><Data ss:Type="Number">47683</Data></Cell><Cell ss:Formula="of:=SUM([.M2:.M5])"><Data ss:Type="Number">39248</Data></Cell></Row><Row ss:Height="12.816"><Cell><Data ss:Type="String">Total</Data></Cell><Cell ss:Index="13" ss:Formula="of:=SUM([.B6:.M6])"><Data ss:Type="Number">328652.5</Data></Cell></Row></Table><x:WorksheetOptions/></ss:Worksheet></Workbook>