Require Import Reals.
Require Import Classical.

Open Scope R_scope.

Theorem intermediate_value_theorem :
  forall (f : R -> R) (a b y : R),
  a < b ->
  continuous_on f (fun x => a <= x <= b) ->
  f a <= y <= f b \/ f b <= y <= f a ->
  exists c, a <= c <= b /\ f c = y.
Proof.
  intros f a b y Hab Hcont Hy.
  destruct Hy as [Hy | Hy].
  - (* Case: f a <= y <= f b *)
    assert (H : exists c, a <= c <= b /\ f c = y).
    {
      apply IVT_le with (f := f) (a := a) (b := b).
      - exact Hab.
      - exact Hcont.
      - exact (proj1 Hy).
      - exact (proj2 Hy).
    }
    exact H.
  - (* Case: f b <= y <= f a *)
    assert (H : exists c, a <= c <= b /\ f c = y).
    {
      apply IVT_ge with (f := f) (a := a) (b := b).
      - exact Hab.
      - exact Hcont.
      - exact (proj2 Hy).
      - exact (proj1 Hy).
    }
    exact H.
Qed.