{"mcpServers": {"ddg-search": {"command": "uvx", "args": ["duckduckgo-mcp-server"], "alwaysAllow": ["search", "fetch_content"]}, "browser-use": {"command": "uvx", "args": ["mcp-server-browser-use@latest"], "env": {"MCP_LLM_OPENROUTER_API_KEY": "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", "MCP_LLM_PROVIDER": "openrouter", "MCP_LLM_MODEL_NAME": "opengvlab/internvl3-14b:free", "MCP_BROWSER_HEADLESS": "false"}, "timeout": 300, "alwaysAllow": ["run_deep_research"]}}}