<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hugging Face Papers Word Cloud</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-cloud/1.2.7/d3.layout.cloud.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .word-cloud-container {
            width: 100%;
            height: 70vh;
            margin: 0 auto;
            position: relative;
            overflow: visible;
        }

        .word {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s ease, color 0.5s ease;
            transform-origin: center center;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .word:hover {
            transform: scale(1.2);
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        .word.selected {
            background-color: rgba(59, 130, 246, 0.2) !important;
            padding: 4px 8px !important;
            border-radius: 4px !important;
            border: 2px solid rgba(59, 130, 246, 0.5);
            font-weight: bold;
            transform: scale(1.1);
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        .paper-card {
            transition: all 0.3s ease;
            transform-origin: top left;
        }

        .paper-card:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        /* Toggle switch styling */
        .toggle-checkbox {
            transition: transform 0.2s ease-in-out;
        }

        #highlight-toggle:checked+label .toggle-checkbox {
            transform: translateX(100%);
            background-color: #4f46e5;
        }

        /* Color scheme selector styling */
        #color-scheme {
            transition: all 0.2s ease;
        }

        #color-scheme:focus {
            outline: none;
            box-shadow: 0 0 0 2px #4f46e5;
            border-color: #4f46e5;
        }

        /* Highlight mode styling */
        .highlight-mode .word {
            transition: all 0.3s ease, color 0.5s ease, filter 0.3s ease;
        }

        .highlight-mode .word:hover {
            filter: brightness(1.2) drop-shadow(0 0 8px currentColor);
            z-index: 10;
            position: relative;
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-indigo-700 mb-4">
                <i class="fas fa-cloud-word mr-2"></i> Hugging Face Papers Word Cloud
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Visualizing research trends from today's AI papers on Hugging Face
            </p>
            <div class="flex justify-center mt-6">
                <button id="refresh-btn"
                    class="flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition-all duration-300">
                    <i class="fas fa-sync-alt mr-2"></i> Refresh Papers
                </button>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loading" class="text-center py-20">
            <div
                class="inline-block animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-500 border-opacity-50 mb-4">
            </div>
            <p class="text-xl text-gray-600">Fetching today's papers from Hugging Face...</p>
        </div>

        <!-- Word Cloud Section -->
        <div id="word-cloud-section" class="hidden mb-16">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-semibold text-gray-800">
                            <i class="fas fa-cloud text-indigo-500 mr-2"></i> Trending Topics
                            <span id="word-count-badge"
                                class="ml-2 text-sm bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full hidden">
                                0 words
                            </span>
                        </h2>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-2">Highlight Mode:</span>
                            <div class="relative inline-block w-12 mr-2 align-middle select-none">
                                <input type="checkbox" id="highlight-toggle" class="sr-only" checked>
                                <label for="highlight-toggle"
                                    class="block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
                                    <span
                                        class="toggle-checkbox block h-6 w-6 rounded-full bg-white shadow-md transform transition ease-in-out duration-200"></span>
                                </label>
                            </div>
                            <select id="color-scheme" class="border rounded px-2 py-1 text-sm bg-white">
                                <option value="default">Default Colors</option>
                                <option value="bright">Bright Colors</option>
                                <option value="pastel">Pastel Colors</option>
                                <option value="monochrome">Monochrome</option>
                            </select>
                        </div>
                    </div>
                    <div class="word-cloud-container bg-gray-50 rounded-lg" id="word-cloud"></div>
                </div>
            </div>

            <!-- Papers List -->
            <div id="papers-container">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold text-gray-800">
                        <i class="fas fa-file-alt text-indigo-500 mr-2"></i> Today's Papers
                        <span id="paper-count-badge"
                            class="ml-2 text-sm bg-green-100 text-green-800 px-2 py-1 rounded-full hidden">
                            0 papers
                        </span>
                    </h2>
                    <div class="relative">
                        <input type="text" id="paper-search" placeholder="Search papers..."
                            class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>
                <div id="papers-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
            </div>
        </div>

        <!-- No Papers State -->
        <div id="no-papers" class="hidden text-center py-20">
            <i class="fas fa-cloud-showers-heavy text-6xl text-gray-400 mb-6"></i>
            <h3 class="text-2xl font-semibold text-gray-700 mb-2">No Papers Found Today</h3>
            <p class="text-gray-500 max-w-lg mx-auto">
                We couldn't find any recent papers on Hugging Face. Try refreshing later or check your connection.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white py-8 border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-500">
                    Made with <i class="fas fa-heart text-red-500"></i> using Hugging Face API
                </p>
                <p class="text-gray-400 text-sm mt-2">
                    Data updates in real-time. Not affiliated with Hugging Face.
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Enhanced Logging System
        class Logger {
            constructor() {
                this.logs = [];
                this.logLevels = {
                    DEBUG: 0,
                    INFO: 1,
                    WARN: 2,
                    ERROR: 3
                };
                this.currentLevel = this.logLevels.INFO;
                this.startTime = Date.now();

                // Add log download button to header
                this.addLogDownloadButton();

                this.log('INFO', 'Logger initialized', { timestamp: new Date().toISOString() });
            }

            log(level, message, data = {}) {
                const timestamp = new Date().toISOString();
                const elapsed = Date.now() - this.startTime;

                const logEntry = {
                    timestamp,
                    elapsed,
                    level,
                    message,
                    data,
                    stack: level === 'ERROR' ? new Error().stack : null
                };

                this.logs.push(logEntry);

                // Also log to console for development
                const consoleMethod = level.toLowerCase() === 'error' ? 'error' :
                    level.toLowerCase() === 'warn' ? 'warn' : 'log';
                console[consoleMethod](`[${level}] ${message}`, data);

                // Store in localStorage for persistence
                try {
                    localStorage.setItem('wordcloud_logs', JSON.stringify(this.logs.slice(-100))); // Keep last 100 logs
                } catch (e) {
                    console.warn('Could not save logs to localStorage:', e);
                }
            }

            addLogDownloadButton() {
                const header = document.querySelector('header .flex');
                if (header) {
                    const logBtn = document.createElement('button');
                    logBtn.id = 'download-logs-btn';
                    logBtn.className = 'ml-4 flex items-center bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-all duration-300';
                    logBtn.innerHTML = '<i class="fas fa-download mr-2"></i> Download Logs';
                    logBtn.addEventListener('click', () => this.downloadLogs());
                    header.appendChild(logBtn);
                }
            }

            downloadLogs() {
                const logData = {
                    generated: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    logs: this.logs
                };

                const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `wordcloud-logs-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.log('INFO', 'Logs downloaded', { logCount: this.logs.length });
            }

            getStats() {
                const stats = {
                    totalLogs: this.logs.length,
                    byLevel: {},
                    errors: this.logs.filter(log => log.level === 'ERROR'),
                    warnings: this.logs.filter(log => log.level === 'WARN')
                };

                Object.keys(this.logLevels).forEach(level => {
                    stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
                });

                return stats;
            }
        }

        // Initialize logger
        const logger = new Logger();

        document.addEventListener('DOMContentLoaded', function () {
            logger.log('INFO', 'DOM Content Loaded', {
                readyState: document.readyState,
                userAgent: navigator.userAgent
            });

            try {
                // UI Elements
                const wordCloudSection = document.getElementById('word-cloud-section');
                const loadingElement = document.getElementById('loading');
                const noPapersElement = document.getElementById('no-papers');
                const papersList = document.getElementById('papers-list');
                const refreshBtn = document.getElementById('refresh-btn');
                const paperSearch = document.getElementById('paper-search');
                const highlightToggle = document.getElementById('highlight-toggle');
                const colorSchemeSelect = document.getElementById('color-scheme');

                // Verify all UI elements are found
                const elements = {
                    wordCloudSection, loadingElement, noPapersElement, papersList,
                    refreshBtn, paperSearch, highlightToggle, colorSchemeSelect
                };

                const missingElements = Object.entries(elements)
                    .filter(([name, element]) => !element)
                    .map(([name]) => name);

                if (missingElements.length > 0) {
                    logger.log('WARN', 'Missing UI elements', { missingElements });
                } else {
                    logger.log('INFO', 'All UI elements found successfully');
                }

                // State
                let papers = [];
                let wordData = [];
                let colorScheme = 'default';

                // Check for external dependencies
                logger.log('INFO', 'Checking external dependencies');
                if (typeof d3 === 'undefined') {
                    logger.log('ERROR', 'D3.js library not loaded');
                    throw new Error('D3.js library is required but not loaded');
                }
                if (typeof d3.layout === 'undefined' || typeof d3.layout.cloud === 'undefined') {
                    logger.log('ERROR', 'D3 word cloud layout not available');
                    throw new Error('D3 word cloud layout is required but not available');
                }
                logger.log('INFO', 'External dependencies verified successfully');

                // Initialize
                logger.log('INFO', 'Starting application initialization');
                fetchPapers();

                // Event Listeners with logging
                logger.log('INFO', 'Setting up event listeners');
                refreshBtn.addEventListener('click', () => {
                    logger.log('INFO', 'Refresh button clicked');
                    fetchPapers();
                });

                paperSearch.addEventListener('input', (e) => {
                    logger.log('DEBUG', 'Search input changed', { value: e.target.value });
                    filterPapers();
                });

                colorSchemeSelect.addEventListener('change', function () {
                    logger.log('INFO', 'Color scheme changed', {
                        oldScheme: colorScheme,
                        newScheme: this.value,
                        element: this.id
                    });
                    colorScheme = this.value;

                    // Update colors smoothly without full re-render
                    if (wordData && wordData.length > 0) {
                        updateWordCloudColors();
                    } else {
                        logger.log('WARN', 'No word data available for color scheme change');
                    }
                });

                highlightToggle.addEventListener('change', function () {
                    logger.log('INFO', 'Highlight toggle changed', { checked: this.checked });
                    const wordCloudContainer = document.getElementById('word-cloud');
                    if (this.checked) {
                        wordCloudContainer.classList.add('highlight-mode');
                        logger.log('INFO', 'Highlight mode enabled - hover over words to highlight related papers');
                    } else {
                        wordCloudContainer.classList.remove('highlight-mode');
                        // Reset any highlighted papers
                        document.querySelectorAll('.paper-card').forEach(card => {
                            card.style.transform = '';
                            card.style.opacity = '';
                        });
                        logger.log('INFO', 'Highlight mode disabled');
                    }
                });

                // Functions
                async function fetchPapers() {
                    const startTime = performance.now();
                    logger.log('INFO', 'Starting to fetch papers');

                    try {
                        // Show loading state
                        logger.log('DEBUG', 'Updating UI to loading state');
                        wordCloudSection.classList.add('hidden');
                        noPapersElement.classList.add('hidden');
                        loadingElement.classList.remove('hidden');

                        // Fetch real papers from Hugging Face API
                        logger.log('INFO', 'Calling fetchHuggingFacePapers');
                        papers = await fetchHuggingFacePapers();

                        const fetchTime = performance.now() - startTime;
                        logger.log('INFO', 'Papers fetched successfully', {
                            paperCount: papers.length,
                            fetchTimeMs: Math.round(fetchTime)
                        });

                        if (papers.length > 0) {
                            // Process papers to extract words
                            logger.log('INFO', 'Processing papers for word cloud');
                            const processStartTime = performance.now();
                            wordData = processPapersForWordCloud(papers);
                            const processTime = performance.now() - processStartTime;

                            logger.log('INFO', 'Word cloud data processed', {
                                wordCount: wordData.length,
                                processTimeMs: Math.round(processTime)
                            });

                            // Update UI badges
                            updateUIBadges();

                            // Render UI
                            logger.log('INFO', 'Rendering UI components');
                            const renderStartTime = performance.now();
                            renderWordCloud();
                            renderPapersList();
                            const renderTime = performance.now() - renderStartTime;

                            logger.log('INFO', 'UI rendering completed', {
                                renderTimeMs: Math.round(renderTime)
                            });

                            wordCloudSection.classList.remove('hidden');
                            noPapersElement.classList.add('hidden');
                        } else {
                            logger.log('WARN', 'No papers found');
                            wordCloudSection.classList.add('hidden');
                            noPapersElement.classList.remove('hidden');
                        }

                        loadingElement.classList.add('hidden');

                        const totalTime = performance.now() - startTime;
                        logger.log('INFO', 'fetchPapers completed successfully', {
                            totalTimeMs: Math.round(totalTime)
                        });

                    } catch (error) {
                        const errorTime = performance.now() - startTime;
                        logger.log('ERROR', 'Error fetching papers', {
                            error: error.message,
                            stack: error.stack,
                            timeMs: Math.round(errorTime)
                        });

                        loadingElement.classList.add('hidden');
                        noPapersElement.classList.remove('hidden');

                        // Show error message to user
                        const errorElement = document.querySelector('#no-papers h3');
                        if (errorElement) {
                            errorElement.textContent = 'Error Loading Papers';
                        }
                        const errorDesc = document.querySelector('#no-papers p');
                        if (errorDesc) {
                            errorDesc.textContent = `An error occurred while loading papers: ${error.message}`;
                        }
                    }
                }

                async function fetchHuggingFacePapers() {
                    logger.log('INFO', 'Fetching real papers from Hugging Face API');

                    try {
                        // Fetch papers from Hugging Face Papers Search API
                        const searchQueries = ['transformer', 'attention', 'neural', 'learning', 'AI'];
                        const randomQuery = searchQueries[Math.floor(Math.random() * searchQueries.length)];

                        const response = await fetch(`https://huggingface.co/api/papers/search?q=${randomQuery}`, {
                            method: 'GET',
                            headers: {
                                'Accept': 'application/json',
                                'User-Agent': 'WordCloud-App/1.0'
                            }
                        });

                        if (!response.ok) {
                            throw new Error(`HTTP error! status: ${response.status}`);
                        }

                        const data = await response.json();
                        logger.log('INFO', 'Raw API response received', {
                            responseSize: JSON.stringify(data).length,
                            dataType: typeof data,
                            isArray: Array.isArray(data),
                            searchQuery: randomQuery
                        });

                        // Process the API response to extract papers
                        let processedPapers = [];

                        if (Array.isArray(data)) {
                            processedPapers = data.slice(0, 15).map((item, index) => {
                                const paper = item.paper || item; // Handle both formats
                                const authors = Array.isArray(paper.authors) ?
                                    paper.authors.map(author => author.name || author).join(', ') :
                                    (paper.authors || 'Unknown Authors');

                                return {
                                    id: paper.id || `paper-${index}`,
                                    title: paper.title || 'Untitled Paper',
                                    abstract: paper.summary || paper.abstract || 'No abstract available.',
                                    authors: authors,
                                    date: paper.publishedAt ? new Date(paper.publishedAt).toISOString().split('T')[0] :
                                        new Date().toISOString().split('T')[0],
                                    url: `https://huggingface.co/papers/${paper.id}`,
                                    tags: extractTagsFromPaper(paper)
                                };
                            });
                        }

                        logger.log('INFO', 'Papers processed successfully', {
                            originalCount: Array.isArray(data) ? data.length : (data.papers?.length || 0),
                            processedCount: processedPapers.length
                        });

                        if (processedPapers.length === 0) {
                            logger.log('ERROR', 'No papers found in API response');
                            throw new Error('No papers found in API response');
                        }

                        return processedPapers;

                    } catch (error) {
                        logger.log('ERROR', 'Failed to fetch from Hugging Face API', {
                            error: error.message,
                            stack: error.stack
                        });

                        // Fallback to curated papers with clear indication
                        logger.log('WARN', 'Using fallback curated papers due to API/CORS issues');
                        return getCuratedPapers();
                    }
                }

                function extractTagsFromPaper(paper) {
                    const tags = [];

                    // Extract from title and abstract
                    const text = `${paper.title || ''} ${paper.summary || paper.abstract || ''}`.toLowerCase();

                    // Common ML/AI keywords to look for
                    const keywords = [
                        'transformer', 'attention', 'bert', 'gpt', 'llm', 'language model',
                        'computer vision', 'nlp', 'machine learning', 'deep learning',
                        'neural network', 'cnn', 'rnn', 'lstm', 'gan', 'diffusion',
                        'reinforcement learning', 'supervised', 'unsupervised',
                        'classification', 'regression', 'clustering', 'optimization',
                        'pytorch', 'tensorflow', 'hugging face', 'dataset'
                    ];

                    keywords.forEach(keyword => {
                        if (text.includes(keyword)) {
                            tags.push(keyword);
                        }
                    });

                    // Add paper-specific tags if available
                    if (paper.tags && Array.isArray(paper.tags)) {
                        tags.push(...paper.tags);
                    }

                    // Ensure we have at least some tags
                    if (tags.length === 0) {
                        tags.push('machine learning', 'AI', 'research');
                    }

                    return [...new Set(tags)]; // Remove duplicates
                }

                function getCuratedPapers() {
                    logger.log('INFO', 'Using curated papers as fallback');
                    // Curated papers with real URLs as fallback
                    const curatedPapers = [
                        {
                            id: '2301.00001',
                            title: 'Attention Is All You Need',
                            abstract: 'The dominant sequence transduction models are based on complex recurrent or convolutional neural networks in an encoder-decoder configuration. The best performing models also connect the encoder and decoder through an attention mechanism.',
                            authors: 'Vaswani et al.',
                            date: '2023-01-01',
                            url: 'https://arxiv.org/abs/1706.03762',
                            tags: ['transformer', 'attention', 'neural networks', 'NLP', 'sequence modeling']
                        },
                        {
                            id: '2301.00002',
                            title: 'BERT: Pre-training of Deep Bidirectional Transformers for Language Understanding',
                            abstract: 'We introduce a new language representation model called BERT, which stands for Bidirectional Encoder Representations from Transformers. BERT is designed to pre-train deep bidirectional representations.',
                            authors: 'Devlin et al.',
                            date: '2023-01-02',
                            url: 'https://arxiv.org/abs/1810.04805',
                            tags: ['BERT', 'transformers', 'language model', 'pre-training', 'NLP']
                        },
                        {
                            id: '2301.00003',
                            title: 'Generative Adversarial Networks',
                            abstract: 'We propose a new framework for estimating generative models via an adversarial process, in which we simultaneously train two models: a generative model G and a discriminative model D.',
                            authors: 'Goodfellow et al.',
                            date: '2023-01-03',
                            url: 'https://arxiv.org/abs/1406.2661',
                            tags: ['GAN', 'generative models', 'adversarial training', 'deep learning']
                        },
                        {
                            id: '2301.00004',
                            title: 'Deep Residual Learning for Image Recognition',
                            abstract: 'Deeper neural networks are more difficult to train. We present a residual learning framework to ease the training of networks that are substantially deeper than those used previously.',
                            authors: 'He et al.',
                            date: '2023-01-04',
                            url: 'https://arxiv.org/abs/1512.03385',
                            tags: ['ResNet', 'computer vision', 'deep learning', 'image recognition', 'CNN']
                        },
                        {
                            id: '2301.00005',
                            title: 'Language Models are Few-Shot Learners',
                            abstract: 'Recent work has demonstrated substantial gains on many NLP tasks and benchmarks by pre-training on a large corpus of text followed by fine-tuning on a specific task.',
                            authors: 'Brown et al.',
                            date: '2023-01-05',
                            url: 'https://arxiv.org/abs/2005.14165',
                            tags: ['GPT-3', 'few-shot learning', 'language models', 'NLP', 'OpenAI']
                        },
                        {
                            id: '2301.00006',
                            title: 'An Image is Worth 16x16 Words: Transformers for Image Recognition at Scale',
                            abstract: 'While the Transformer architecture has become the de-facto standard for natural language processing tasks, its applications to computer vision remain limited.',
                            authors: 'Dosovitskiy et al.',
                            date: '2023-01-06',
                            url: 'https://arxiv.org/abs/2010.11929',
                            tags: ['Vision Transformer', 'ViT', 'computer vision', 'transformers', 'image recognition']
                        },
                        {
                            id: '2301.00007',
                            title: 'CLIP: Learning Transferable Visual Representations from Natural Language Supervision',
                            abstract: 'State-of-the-art computer vision systems are trained to predict a fixed set of predetermined object categories. This restricted form of supervision limits their generality and usability.',
                            authors: 'Radford et al.',
                            date: '2023-01-07',
                            url: 'https://arxiv.org/abs/2103.00020',
                            tags: ['CLIP', 'multimodal', 'vision-language', 'zero-shot', 'OpenAI']
                        },
                        {
                            id: '2301.00008',
                            title: 'Denoising Diffusion Probabilistic Models',
                            abstract: 'We present high quality image synthesis results using diffusion probabilistic models, a class of latent variable models inspired by considerations from nonequilibrium thermodynamics.',
                            authors: 'Ho et al.',
                            date: '2023-01-08',
                            url: 'https://arxiv.org/abs/2006.11239',
                            tags: ['diffusion models', 'image generation', 'probabilistic models', 'denoising']
                        },
                        {
                            id: '2301.00009',
                            title: 'MobileNets: Efficient Convolutional Neural Networks for Mobile Vision Applications',
                            abstract: 'We present a class of efficient models called MobileNets for mobile and embedded vision applications. MobileNets are based on a streamlined architecture.',
                            authors: 'Howard et al.',
                            date: '2023-01-09',
                            url: 'https://arxiv.org/abs/1704.04861',
                            tags: ['MobileNet', 'mobile computing', 'efficient networks', 'computer vision', 'edge AI']
                        },
                        {
                            id: '2301.00010',
                            title: 'Neural Machine Translation by Jointly Learning to Align and Translate',
                            abstract: 'Neural machine translation is a recently proposed approach to machine translation. Unlike the traditional statistical machine translation, the neural machine translation aims at building a single neural network.',
                            authors: 'Bahdanau et al.',
                            date: '2023-01-10',
                            url: 'https://arxiv.org/abs/1409.0473',
                            tags: ['neural machine translation', 'attention mechanism', 'sequence-to-sequence', 'NLP']
                        }
                    ];

                    logger.log('INFO', 'Curated papers generated', { paperCount: curatedPapers.length });
                    return curatedPapers;
                }

                function updateUIBadges() {
                    logger.log('DEBUG', 'Updating UI badges', {
                        paperCount: papers.length,
                        wordCount: wordData.length
                    });

                    // Update word count badge
                    const wordCountBadge = document.getElementById('word-count-badge');
                    if (wordCountBadge) {
                        wordCountBadge.textContent = `${wordData.length} words`;
                        wordCountBadge.classList.remove('hidden');
                    }

                    // Update paper count badge
                    const paperCountBadge = document.getElementById('paper-count-badge');
                    if (paperCountBadge) {
                        paperCountBadge.textContent = `${papers.length} papers`;
                        paperCountBadge.classList.remove('hidden');
                    }
                }

                function processPapersForWordCloud(papers) {
                    logger.log('INFO', 'Processing papers for word cloud', { paperCount: papers.length });

                    try {
                        // Combine all text fields from papers
                        const allText = papers.map(paper =>
                            `${paper.title} ${paper.abstract} ${paper.tags.join(' ')}`
                        ).join(' ');

                        logger.log('DEBUG', 'Text extraction completed', { textLength: allText.length });

                        // Common words to exclude
                        const stopWords = new Set(['the', 'and', 'for', 'with', 'this', 'that', 'these', 'those', 'are', 'was', 'were', 'have', 'has', 'had', 'a', 'an', 'in', 'on', 'at', 'to', 'of', 'by', 'we', 'our', 'as', 'be', 'is', 'can', 'how', 'what', 'which', 'it', 'its', 'from']);

                        // Extract words and count frequencies
                        const wordCounts = {};
                        const words = allText.toLowerCase().match(/\b[\w'-]+\b/g) || [];

                        logger.log('DEBUG', 'Word extraction completed', { totalWords: words.length });

                        words.forEach(word => {
                            if (!stopWords.has(word) && word.length > 2) {
                                wordCounts[word] = (wordCounts[word] || 0) + 1;
                            }
                        });

                        // Convert to array and sort by frequency
                        const result = Object.entries(wordCounts)
                            .map(([text, value]) => ({ text, value }))
                            .sort((a, b) => b.value - a.value)
                            .slice(0, 50); // Limit to top 50 words

                        logger.log('INFO', 'Word cloud processing completed', {
                            uniqueWords: Object.keys(wordCounts).length,
                            topWords: result.length,
                            mostFrequent: result[0]?.text || 'none'
                        });

                        return result;
                    } catch (error) {
                        logger.log('ERROR', 'Error processing papers for word cloud', {
                            error: error.message,
                            stack: error.stack
                        });
                        return [];
                    }
                }

                function updateWordCloudColors() {
                    logger.log('DEBUG', 'Updating word cloud colors', { colorScheme });

                    // Color schemes
                    const colors = {
                        default: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899'],
                        bright: ['#FF5733', '#33FF57', '#3357FF', '#FF33A8', '#33FFF5', '#F5FF33'],
                        pastel: ['#A5D6A7', '#81D4FA', '#CE93D8', '#FFF59D', '#FFAB91', '#90CAF9'],
                        monochrome: ['#4a5568', '#718096', '#a0aec0', '#cbd5e0', '#e2e8f0']
                    };

                    const currentColors = colors[colorScheme];
                    const wordElements = document.querySelectorAll('.word');

                    wordElements.forEach((element, i) => {
                        const wordIndex = parseInt(element.dataset.wordIndex) || i;
                        element.style.color = currentColors[wordIndex % currentColors.length];
                    });

                    logger.log('INFO', 'Word cloud colors updated', {
                        colorScheme,
                        elementsUpdated: wordElements.length
                    });
                }

                function renderWordCloud() {
                    logger.log('INFO', 'Starting word cloud rendering');

                    try {
                        const container = document.getElementById('word-cloud');
                        if (!container) {
                            throw new Error('Word cloud container not found');
                        }

                        container.innerHTML = '';

                        const width = container.clientWidth;
                        const height = container.clientHeight;

                        logger.log('DEBUG', 'Container dimensions', { width, height });

                        if (width === 0 || height === 0) {
                            logger.log('WARN', 'Container has zero dimensions, retrying in 100ms');
                            setTimeout(() => renderWordCloud(), 100);
                            return;
                        }

                        if (!wordData || wordData.length === 0) {
                            logger.log('WARN', 'No word data available for rendering');
                            container.innerHTML = '<div class="text-center text-gray-500 p-8">No words to display</div>';
                            return;
                        }

                        // Color schemes
                        const colors = {
                            default: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899'],
                            bright: ['#FF5733', '#33FF57', '#3357FF', '#FF33A8', '#33FFF5', '#F5FF33'],
                            pastel: ['#A5D6A7', '#81D4FA', '#CE93D8', '#FFF59D', '#FFAB91', '#90CAF9'],
                            monochrome: ['#4a5568', '#718096', '#a0aec0', '#cbd5e0', '#e2e8f0']
                        };

                        // Get selected color scheme
                        const currentColors = colors[colorScheme];
                        logger.log('DEBUG', 'Using color scheme', { colorScheme, colorCount: currentColors.length });

                        // Generate positions using D3 layout
                        logger.log('DEBUG', 'Initializing D3 word cloud layout', { wordCount: wordData.length });

                        const layout = d3.layout.cloud()
                            .size([width, height])
                            .words(wordData)
                            .padding(5)
                            .rotate(() => (Math.random() > 0.5 ? 0 : 90))
                            .fontSize(d => Math.min(Math.max(14, d.value * 5), 50))
                            .on('end', draw)
                            .on('word', (word) => {
                                logger.log('DEBUG', 'Word positioned', { text: word.text, x: word.x, y: word.y });
                            });

                        layout.start();

                        function draw(words) {
                            logger.log('INFO', 'Drawing word cloud', { wordCount: words.length });

                            try {
                                words.forEach((word, i) => {
                                    const element = document.createElement('div');
                                    element.className = 'word';
                                    element.style.fontSize = `${word.size}px`;
                                    element.style.color = currentColors[i % currentColors.length];
                                    element.style.left = `${word.x + width / 2 - word.size / 2}px`;
                                    element.style.top = `${word.y + height / 2 - word.size / 2}px`;
                                    element.style.opacity = word.value / Math.max(...wordData.map(w => w.value)) * 0.8 + 0.2;
                                    element.style.fontWeight = 100 + (word.value * 10);
                                    element.textContent = word.text;
                                    element.dataset.wordIndex = i; // Store index for color updates

                                    // Add some random floating animation to some words
                                    if (Math.random() > 0.7) {
                                        element.classList.add('floating');
                                        element.style.animationDelay = `${Math.random() * 3}s`;
                                    }

                                    // Highlight papers containing this word on hover
                                    element.addEventListener('mouseenter', () => {
                                        logger.log('DEBUG', 'Word hovered', { word: word.text });
                                        if (highlightToggle.checked) {
                                            const papersWithWord = papers.filter(paper =>
                                                paper.title.toLowerCase().includes(word.text) ||
                                                paper.abstract.toLowerCase().includes(word.text) ||
                                                paper.tags.includes(word.text)
                                            );

                                            document.querySelectorAll('.paper-card').forEach(card => {
                                                const paperId = card.dataset.paperId;
                                                const shouldHighlight = papersWithWord.some(p => p.id === paperId);
                                                card.style.transform = shouldHighlight ? 'scale(1.05)' : 'scale(0.95)';
                                                card.style.opacity = shouldHighlight ? '1' : '0.6';
                                            });
                                        }
                                    });

                                    element.addEventListener('mouseleave', () => {
                                        if (highlightToggle.checked) {
                                            document.querySelectorAll('.paper-card').forEach(card => {
                                                card.style.transform = '';
                                                card.style.opacity = '';
                                            });
                                        }
                                    });

                                    // Add click functionality to words
                                    element.addEventListener('click', () => {
                                        logger.log('INFO', 'Word clicked', { word: word.text, value: word.value });

                                        // Check if word is already selected
                                        const isSelected = element.classList.contains('selected');

                                        if (isSelected) {
                                            // Unselect word and clear filter
                                            element.classList.remove('selected');
                                            element.style.backgroundColor = '';
                                            element.style.padding = '';
                                            element.style.borderRadius = '';

                                            // Clear search and show all papers
                                            const searchInput = document.getElementById('paper-search');
                                            if (searchInput) {
                                                searchInput.value = '';
                                                filterPapers();
                                            }

                                            logger.log('INFO', 'Word unselected, showing all papers');
                                        } else {
                                            // Clear any previously selected words
                                            document.querySelectorAll('.word.selected').forEach(selectedWord => {
                                                selectedWord.classList.remove('selected');
                                                selectedWord.style.backgroundColor = '';
                                                selectedWord.style.padding = '';
                                                selectedWord.style.borderRadius = '';
                                            });

                                            // Select this word
                                            element.classList.add('selected');
                                            element.style.backgroundColor = 'rgba(59, 130, 246, 0.2)';
                                            element.style.padding = '4px 8px';
                                            element.style.borderRadius = '4px';

                                            // Filter papers by the clicked word
                                            const searchInput = document.getElementById('paper-search');
                                            if (searchInput) {
                                                searchInput.value = word.text;
                                                filterPapers();
                                            }

                                            logger.log('INFO', 'Word selected and papers filtered', {
                                                word: word.text
                                            });
                                        }
                                    });

                                    // Add hover cursor
                                    element.style.cursor = 'pointer';

                                    container.appendChild(element);
                                });

                                logger.log('INFO', 'Word cloud rendering completed successfully');
                            } catch (error) {
                                logger.log('ERROR', 'Error in draw function', {
                                    error: error.message,
                                    stack: error.stack
                                });
                            }
                        }

                    } catch (error) {
                        logger.log('ERROR', 'Error rendering word cloud', {
                            error: error.message,
                            stack: error.stack
                        });

                        // Show error message in container
                        const container = document.getElementById('word-cloud');
                        if (container) {
                            container.innerHTML = `<div class="text-center text-red-500 p-8">Error rendering word cloud: ${error.message}</div>`;
                        }
                    }
                }

                function renderPapersList() {
                    logger.log('INFO', 'Rendering papers list', { paperCount: papers.length });

                    try {
                        papersList.innerHTML = '';

                        papers.forEach(paper => {
                            const card = document.createElement('div');
                            card.className = 'paper-card bg-white rounded-lg shadow-md overflow-hidden h-full flex flex-col';
                            card.dataset.paperId = paper.id;

                            card.innerHTML = `
                            <div class="p-6 flex-grow">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold text-gray-800 mb-2">${paper.title}</h3>
                                    <span class="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
                                        ${paper.date}
                                    </span>
                                </div>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">${paper.abstract}</p>
                                <div class="text-sm text-gray-500 mb-4">By ${paper.authors}</div>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    ${paper.tags.map(tag => `
                                        <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${tag}</span>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                                <a href="${paper.url}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium" target="_blank">
                                    <i class="fas fa-external-link-alt mr-1"></i> Read Paper
                                </a>
                                <button class="text-gray-500 hover:text-gray-700">
                                    <i class="far fa-bookmark"></i>
                                </button>
                            </div>
                        `;

                            papersList.appendChild(card);
                        });

                        logger.log('INFO', 'Papers list rendered successfully');
                    } catch (error) {
                        logger.log('ERROR', 'Error rendering papers list', {
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }

                function filterPapers() {
                    const searchTerm = paperSearch.value.toLowerCase();
                    logger.log('DEBUG', 'Filtering papers', { searchTerm });

                    try {
                        const cards = document.querySelectorAll('.paper-card');
                        let visibleCount = 0;

                        cards.forEach(card => {
                            const paperId = card.dataset.paperId;
                            const paper = papers.find(p => p.id === paperId);

                            const matches = searchTerm === '' ||
                                paper.title.toLowerCase().includes(searchTerm) ||
                                paper.abstract.toLowerCase().includes(searchTerm) ||
                                paper.authors.toLowerCase().includes(searchTerm) ||
                                paper.tags.some(tag => tag.toLowerCase().includes(searchTerm));

                            card.style.display = matches ? '' : 'none';
                            if (matches) visibleCount++;
                        });

                        logger.log('INFO', 'Papers filtered', {
                            searchTerm,
                            totalCards: cards.length,
                            visibleCount
                        });
                    } catch (error) {
                        logger.log('ERROR', 'Error filtering papers', {
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }

                // Global error handler
                window.addEventListener('error', (event) => {
                    logger.log('ERROR', 'Global error caught', {
                        message: event.message,
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno,
                        error: event.error?.stack
                    });
                });

                window.addEventListener('unhandledrejection', (event) => {
                    logger.log('ERROR', 'Unhandled promise rejection', {
                        reason: event.reason,
                        promise: event.promise
                    });
                });

                logger.log('INFO', 'Application initialization completed');

            } catch (error) {
                logger.log('ERROR', 'Critical error during initialization', {
                    error: error.message,
                    stack: error.stack
                });

                // Show critical error to user
                document.body.innerHTML = `
                    <div class="min-h-screen flex items-center justify-center bg-red-50">
                        <div class="text-center">
                            <h1 class="text-2xl font-bold text-red-600 mb-4">Application Error</h1>
                            <p class="text-red-500 mb-4">A critical error occurred during initialization:</p>
                            <p class="text-sm text-gray-600 bg-white p-4 rounded border">${error.message}</p>
                            <button onclick="location.reload()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                Reload Page
                            </button>
                        </div>
                    </div>
                `;
            }
        });
    </script>
</body>

</html>