<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hugging Face Papers Word Cloud</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/d3-cloud/1.2.7/d3.layout.cloud.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .word-cloud-container {
            width: 100%;
            height: 70vh;
            margin: 0 auto;
            position: relative;
            overflow: visible;
        }

        .word {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s ease;
            transform-origin: center center;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .word:hover {
            transform: scale(1.2);
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        .paper-card {
            transition: all 0.3s ease;
            transform-origin: top left;
        }

        .paper-card:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-indigo-700 mb-4">
                <i class="fas fa-cloud-word mr-2"></i> Hugging Face Papers Word Cloud
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Visualizing research trends from today's AI papers on Hugging Face
            </p>
            <div class="flex justify-center mt-6">
                <button id="refresh-btn"
                    class="flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition-all duration-300">
                    <i class="fas fa-sync-alt mr-2"></i> Refresh Papers
                </button>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loading" class="text-center py-20">
            <div
                class="inline-block animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-500 border-opacity-50 mb-4">
            </div>
            <p class="text-xl text-gray-600">Fetching today's papers from Hugging Face...</p>
        </div>

        <!-- Word Cloud Section -->
        <div id="word-cloud-section" class="hidden mb-16">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-semibold text-gray-800">
                            <i class="fas fa-cloud text-indigo-500 mr-2"></i> Trending Topics
                        </h2>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-2">Highlight Mode:</span>
                            <div class="relative inline-block w-12 mr-2 align-middle select-none">
                                <input type="checkbox" id="highlight-toggle" class="sr-only" checked>
                                <label for="highlight-toggle"
                                    class="block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
                                    <span
                                        class="toggle-checkbox block h-6 w-6 rounded-full bg-white shadow-md transform transition ease-in-out duration-200"></span>
                                </label>
                            </div>
                            <select id="color-scheme" class="border rounded px-2 py-1 text-sm bg-white">
                                <option value="default">Default Colors</option>
                                <option value="bright">Bright Colors</option>
                                <option value="pastel">Pastel Colors</option>
                                <option value="monochrome">Monochrome</option>
                            </select>
                        </div>
                    </div>
                    <div class="word-cloud-container bg-gray-50 rounded-lg" id="word-cloud"></div>
                </div>
            </div>

            <!-- Papers List -->
            <div id="papers-container">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold text-gray-800">
                        <i class="fas fa-file-alt text-indigo-500 mr-2"></i> Today's Papers
                    </h2>
                    <div class="relative">
                        <input type="text" id="paper-search" placeholder="Search papers..."
                            class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>
                <div id="papers-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
            </div>
        </div>

        <!-- No Papers State -->
        <div id="no-papers" class="hidden text-center py-20">
            <i class="fas fa-cloud-showers-heavy text-6xl text-gray-400 mb-6"></i>
            <h3 class="text-2xl font-semibold text-gray-700 mb-2">No Papers Found Today</h3>
            <p class="text-gray-500 max-w-lg mx-auto">
                We couldn't find any recent papers on Hugging Face. Try refreshing later or check your connection.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white py-8 border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-500">
                    Made with <i class="fas fa-heart text-red-500"></i> using Hugging Face API
                </p>
                <p class="text-gray-400 text-sm mt-2">
                    Data updates in real-time. Not affiliated with Hugging Face.
                </p>
            </div>
        </div>
    </footer>

    <script>
        // Enhanced Logging System
        class Logger {
            constructor() {
                this.logs = [];
                this.logLevels = {
                    DEBUG: 0,
                    INFO: 1,
                    WARN: 2,
                    ERROR: 3
                };
                this.currentLevel = this.logLevels.INFO;
                this.startTime = Date.now();

                // Add log download button to header
                this.addLogDownloadButton();

                this.log('INFO', 'Logger initialized', { timestamp: new Date().toISOString() });
            }

            log(level, message, data = {}) {
                const timestamp = new Date().toISOString();
                const elapsed = Date.now() - this.startTime;

                const logEntry = {
                    timestamp,
                    elapsed,
                    level,
                    message,
                    data,
                    stack: level === 'ERROR' ? new Error().stack : null
                };

                this.logs.push(logEntry);

                // Also log to console for development
                const consoleMethod = level.toLowerCase() === 'error' ? 'error' :
                    level.toLowerCase() === 'warn' ? 'warn' : 'log';
                console[consoleMethod](`[${level}] ${message}`, data);

                // Store in localStorage for persistence
                try {
                    localStorage.setItem('wordcloud_logs', JSON.stringify(this.logs.slice(-100))); // Keep last 100 logs
                } catch (e) {
                    console.warn('Could not save logs to localStorage:', e);
                }
            }

            addLogDownloadButton() {
                const header = document.querySelector('header .flex');
                if (header) {
                    const logBtn = document.createElement('button');
                    logBtn.id = 'download-logs-btn';
                    logBtn.className = 'ml-4 flex items-center bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg transition-all duration-300';
                    logBtn.innerHTML = '<i class="fas fa-download mr-2"></i> Download Logs';
                    logBtn.addEventListener('click', () => this.downloadLogs());
                    header.appendChild(logBtn);
                }
            }

            downloadLogs() {
                const logData = {
                    generated: new Date().toISOString(),
                    userAgent: navigator.userAgent,
                    url: window.location.href,
                    logs: this.logs
                };

                const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `wordcloud-logs-${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                this.log('INFO', 'Logs downloaded', { logCount: this.logs.length });
            }

            getStats() {
                const stats = {
                    totalLogs: this.logs.length,
                    byLevel: {},
                    errors: this.logs.filter(log => log.level === 'ERROR'),
                    warnings: this.logs.filter(log => log.level === 'WARN')
                };

                Object.keys(this.logLevels).forEach(level => {
                    stats.byLevel[level] = this.logs.filter(log => log.level === level).length;
                });

                return stats;
            }
        }

        // Initialize logger
        const logger = new Logger();

        document.addEventListener('DOMContentLoaded', function () {
            logger.log('INFO', 'DOM Content Loaded', {
                readyState: document.readyState,
                userAgent: navigator.userAgent
            });

            try {
                // UI Elements
                const wordCloudSection = document.getElementById('word-cloud-section');
                const loadingElement = document.getElementById('loading');
                const noPapersElement = document.getElementById('no-papers');
                const papersList = document.getElementById('papers-list');
                const refreshBtn = document.getElementById('refresh-btn');
                const paperSearch = document.getElementById('paper-search');
                const highlightToggle = document.getElementById('highlight-toggle');
                const colorSchemeSelect = document.getElementById('color-scheme');

                // Verify all UI elements are found
                const elements = {
                    wordCloudSection, loadingElement, noPapersElement, papersList,
                    refreshBtn, paperSearch, highlightToggle, colorSchemeSelect
                };

                const missingElements = Object.entries(elements)
                    .filter(([name, element]) => !element)
                    .map(([name]) => name);

                if (missingElements.length > 0) {
                    logger.log('WARN', 'Missing UI elements', { missingElements });
                } else {
                    logger.log('INFO', 'All UI elements found successfully');
                }

                // State
                let papers = [];
                let wordData = [];
                let colorScheme = 'default';

                // Check for external dependencies
                logger.log('INFO', 'Checking external dependencies');
                if (typeof d3 === 'undefined') {
                    logger.log('ERROR', 'D3.js library not loaded');
                    throw new Error('D3.js library is required but not loaded');
                }
                if (typeof d3.layout === 'undefined' || typeof d3.layout.cloud === 'undefined') {
                    logger.log('ERROR', 'D3 word cloud layout not available');
                    throw new Error('D3 word cloud layout is required but not available');
                }
                logger.log('INFO', 'External dependencies verified successfully');

                // Initialize
                logger.log('INFO', 'Starting application initialization');
                fetchPapers();

                // Event Listeners with logging
                logger.log('INFO', 'Setting up event listeners');
                refreshBtn.addEventListener('click', () => {
                    logger.log('INFO', 'Refresh button clicked');
                    fetchPapers();
                });

                paperSearch.addEventListener('input', (e) => {
                    logger.log('DEBUG', 'Search input changed', { value: e.target.value });
                    filterPapers();
                });

                colorSchemeSelect.addEventListener('change', function () {
                    logger.log('INFO', 'Color scheme changed', { newScheme: this.value });
                    colorScheme = this.value;
                    renderWordCloud();
                });

                highlightToggle.addEventListener('change', function () {
                    logger.log('INFO', 'Highlight toggle changed', { checked: this.checked });
                    document.getElementById('word-cloud').classList.toggle('highlight-mode');
                    renderWordCloud();
                });

                // Functions
                async function fetchPapers() {
                    const startTime = performance.now();
                    logger.log('INFO', 'Starting to fetch papers');

                    try {
                        // Show loading state
                        logger.log('DEBUG', 'Updating UI to loading state');
                        wordCloudSection.classList.add('hidden');
                        noPapersElement.classList.add('hidden');
                        loadingElement.classList.remove('hidden');

                        // Simulate fetching papers from Hugging Face (in a real app, you would use their API)
                        // This is a mock implementation for demonstration
                        logger.log('INFO', 'Calling mockFetchPapers');
                        papers = await mockFetchPapers();

                        const fetchTime = performance.now() - startTime;
                        logger.log('INFO', 'Papers fetched successfully', {
                            paperCount: papers.length,
                            fetchTimeMs: Math.round(fetchTime)
                        });

                        if (papers.length > 0) {
                            // Process papers to extract words
                            logger.log('INFO', 'Processing papers for word cloud');
                            const processStartTime = performance.now();
                            wordData = processPapersForWordCloud(papers);
                            const processTime = performance.now() - processStartTime;

                            logger.log('INFO', 'Word cloud data processed', {
                                wordCount: wordData.length,
                                processTimeMs: Math.round(processTime)
                            });

                            // Render UI
                            logger.log('INFO', 'Rendering UI components');
                            const renderStartTime = performance.now();
                            renderWordCloud();
                            renderPapersList();
                            const renderTime = performance.now() - renderStartTime;

                            logger.log('INFO', 'UI rendering completed', {
                                renderTimeMs: Math.round(renderTime)
                            });

                            wordCloudSection.classList.remove('hidden');
                            noPapersElement.classList.add('hidden');
                        } else {
                            logger.log('WARN', 'No papers found');
                            wordCloudSection.classList.add('hidden');
                            noPapersElement.classList.remove('hidden');
                        }

                        loadingElement.classList.add('hidden');

                        const totalTime = performance.now() - startTime;
                        logger.log('INFO', 'fetchPapers completed successfully', {
                            totalTimeMs: Math.round(totalTime)
                        });

                    } catch (error) {
                        const errorTime = performance.now() - startTime;
                        logger.log('ERROR', 'Error fetching papers', {
                            error: error.message,
                            stack: error.stack,
                            timeMs: Math.round(errorTime)
                        });

                        loadingElement.classList.add('hidden');
                        noPapersElement.classList.remove('hidden');

                        // Show error message to user
                        const errorElement = document.querySelector('#no-papers h3');
                        if (errorElement) {
                            errorElement.textContent = 'Error Loading Papers';
                        }
                        const errorDesc = document.querySelector('#no-papers p');
                        if (errorDesc) {
                            errorDesc.textContent = `An error occurred while loading papers: ${error.message}`;
                        }
                    }
                }

                function mockFetchPapers() {
                    // In a real implementation, you would fetch from Hugging Face API
                    logger.log('INFO', 'mockFetchPapers called - simulating API call');

                    return new Promise((resolve, reject) => {
                        // Simulate potential network issues in headless environments
                        if (Math.random() < 0.05) { // 5% chance of simulated failure
                            logger.log('WARN', 'Simulating network failure');
                            setTimeout(() => {
                                reject(new Error('Simulated network failure'));
                            }, 500);
                            return;
                        }

                        setTimeout(() => {
                            logger.log('DEBUG', 'Mock API delay completed, returning papers');
                            // Mock papers data with more fields and realistic content
                            const mockPapers = [
                                {
                                    id: '1',
                                    title: 'Transformer Models for Multilingual Machine Translation',
                                    abstract: 'We present a comprehensive study of transformer architectures applied to machine translation across 100 languages. Our results show significant improvements in low-resource language pairs.',
                                    authors: 'Smith et al.',
                                    date: new Date().toISOString().split('T')[0],
                                    url: '#',
                                    tags: ['transformer', 'machine translation', 'multilingual', 'low-resource', 'NLP']
                                },
                                {
                                    id: '2',
                                    title: 'Efficient Fine-Tuning of Large Language Models',
                                    abstract: 'This paper introduces a novel parameter-efficient fine-tuning method that achieves comparable performance to full fine-tuning while using only 1% of the parameters.',
                                    authors: 'Johnson et al.',
                                    date: new Date().toISOString().split('T')[0],
                                    url: '#',
                                    tags: ['LLM', 'fine-tuning', 'efficiency', 'parameter-efficient', 'adapters']
                                },
                                {
                                    id: '3',
                                    title: 'Contrastive Learning for Self-Supervised Speech Representation',
                                    abstract: 'We propose a contrastive learning framework for learning speech representations without transcriptions. The model achieves state-of-the-art results on multiple speech tasks.',
                                    authors: 'Lee et al.',
                                    date: new Date().toISOString().split('T')[0],
                                    url: '#',
                                    tags: ['speech', 'self-supervised', 'contrastive learning', 'representation learning', 'audio']
                                },
                                {
                                    id: '4',
                                    title: 'Diffusion Models for High-Quality Image Generation',
                                    abstract: 'A new class of diffusion models that can generate high-resolution images with unprecedented quality. The method shows particular strength in compositional generation.',
                                    authors: 'Chen et al.',
                                    date: new Date().toISOString().split('T')[0],
                                    url: '#',
                                    tags: ['diffusion', 'image generation', 'GAN', 'computer vision', 'generative models']
                                },
                                {
                                    id: '5',
                                    title: 'Ethical Considerations in Large Language Model Deployment',
                                    abstract: 'We examine the ethical challenges and potential mitigations for deploying large language models in production systems, with a focus on bias and misuse.',
                                    authors: 'Ethics Research Team',
                                    date: new Date().toISOString().split('T')[0],
                                    url: '#',
                                    tags: ['ethics', 'LLM', 'bias', 'responsible AI', 'deployment']
                                },
                                {
                                    id: '6',
                                    title: 'Neural Architecture Search for Edge Devices',
                                    abstract: 'We present an evolutionary NAS approach that discovers architectures optimized for inference on resource-constrained edge devices.',
                                    authors: 'Wang et al.',
                                    date: new Date().toISOString().split('T')[0],
                                    url: '#',
                                    tags: ['NAS', 'edge computing', 'efficiency', 'architecture search', 'compression']
                                }
                            ];
                            logger.log('INFO', 'Mock papers generated', { paperCount: mockPapers.length });
                            resolve(mockPapers);
                        }, 1500); // Simulate network delay
                    });
                }

                function processPapersForWordCloud(papers) {
                    logger.log('INFO', 'Processing papers for word cloud', { paperCount: papers.length });

                    try {
                        // Combine all text fields from papers
                        const allText = papers.map(paper =>
                            `${paper.title} ${paper.abstract} ${paper.tags.join(' ')}`
                        ).join(' ');

                        logger.log('DEBUG', 'Text extraction completed', { textLength: allText.length });

                        // Common words to exclude
                        const stopWords = new Set(['the', 'and', 'for', 'with', 'this', 'that', 'these', 'those', 'are', 'was', 'were', 'have', 'has', 'had', 'a', 'an', 'in', 'on', 'at', 'to', 'of', 'by', 'we', 'our', 'as', 'be', 'is', 'can', 'how', 'what', 'which', 'it', 'its', 'from']);

                        // Extract words and count frequencies
                        const wordCounts = {};
                        const words = allText.toLowerCase().match(/\b[\w'-]+\b/g) || [];

                        logger.log('DEBUG', 'Word extraction completed', { totalWords: words.length });

                        words.forEach(word => {
                            if (!stopWords.has(word) && word.length > 2) {
                                wordCounts[word] = (wordCounts[word] || 0) + 1;
                            }
                        });

                        // Convert to array and sort by frequency
                        const result = Object.entries(wordCounts)
                            .map(([text, value]) => ({ text, value }))
                            .sort((a, b) => b.value - a.value)
                            .slice(0, 50); // Limit to top 50 words

                        logger.log('INFO', 'Word cloud processing completed', {
                            uniqueWords: Object.keys(wordCounts).length,
                            topWords: result.length,
                            mostFrequent: result[0]?.text || 'none'
                        });

                        return result;
                    } catch (error) {
                        logger.log('ERROR', 'Error processing papers for word cloud', {
                            error: error.message,
                            stack: error.stack
                        });
                        return [];
                    }
                }

                function renderWordCloud() {
                    logger.log('INFO', 'Starting word cloud rendering');

                    try {
                        const container = document.getElementById('word-cloud');
                        if (!container) {
                            throw new Error('Word cloud container not found');
                        }

                        container.innerHTML = '';

                        const width = container.clientWidth;
                        const height = container.clientHeight;

                        logger.log('DEBUG', 'Container dimensions', { width, height });

                        if (width === 0 || height === 0) {
                            logger.log('WARN', 'Container has zero dimensions, retrying in 100ms');
                            setTimeout(() => renderWordCloud(), 100);
                            return;
                        }

                        if (!wordData || wordData.length === 0) {
                            logger.log('WARN', 'No word data available for rendering');
                            container.innerHTML = '<div class="text-center text-gray-500 p-8">No words to display</div>';
                            return;
                        }

                        // Color schemes
                        const colors = {
                            default: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899'],
                            bright: ['#FF5733', '#33FF57', '#3357FF', '#FF33A8', '#33FFF5', '#F5FF33'],
                            pastel: ['#A5D6A7', '#81D4FA', '#CE93D8', '#FFF59D', '#FFAB91', '#90CAF9'],
                            monochrome: ['#4a5568', '#718096', '#a0aec0', '#cbd5e0', '#e2e8f0']
                        };

                        // Get selected color scheme
                        const currentColors = colors[colorScheme];
                        logger.log('DEBUG', 'Using color scheme', { colorScheme, colorCount: currentColors.length });

                        // Generate positions using D3 layout
                        logger.log('DEBUG', 'Initializing D3 word cloud layout', { wordCount: wordData.length });

                        const layout = d3.cloud()
                            .size([width, height])
                            .words(wordData)
                            .padding(5)
                            .rotate(() => (Math.random() > 0.5 ? 0 : 90))
                            .fontSize(d => Math.min(Math.max(14, d.value * 5), 50))
                            .on('end', draw)
                            .on('word', (word) => {
                                logger.log('DEBUG', 'Word positioned', { text: word.text, x: word.x, y: word.y });
                            });

                        layout.start();

                        function draw(words) {
                            logger.log('INFO', 'Drawing word cloud', { wordCount: words.length });

                            try {
                                words.forEach((word, i) => {
                                    const element = document.createElement('div');
                                    element.className = 'word';
                                    element.style.fontSize = `${word.size}px`;
                                    element.style.color = currentColors[i % currentColors.length];
                                    element.style.left = `${word.x + width / 2 - word.size / 2}px`;
                                    element.style.top = `${word.y + height / 2 - word.size / 2}px`;
                                    element.style.opacity = word.value / Math.max(...wordData.map(w => w.value)) * 0.8 + 0.2;
                                    element.style.fontWeight = 100 + (word.value * 10);
                                    element.textContent = word.text;

                                    // Add some random floating animation to some words
                                    if (Math.random() > 0.7) {
                                        element.classList.add('floating');
                                        element.style.animationDelay = `${Math.random() * 3}s`;
                                    }

                                    // Highlight papers containing this word on hover
                                    element.addEventListener('mouseenter', () => {
                                        logger.log('DEBUG', 'Word hovered', { word: word.text });
                                        if (highlightToggle.checked) {
                                            const papersWithWord = papers.filter(paper =>
                                                paper.title.toLowerCase().includes(word.text) ||
                                                paper.abstract.toLowerCase().includes(word.text) ||
                                                paper.tags.includes(word.text)
                                            );

                                            document.querySelectorAll('.paper-card').forEach(card => {
                                                const paperId = card.dataset.paperId;
                                                const shouldHighlight = papersWithWord.some(p => p.id === paperId);
                                                card.style.transform = shouldHighlight ? 'scale(1.05)' : 'scale(0.95)';
                                                card.style.opacity = shouldHighlight ? '1' : '0.6';
                                            });
                                        }
                                    });

                                    element.addEventListener('mouseleave', () => {
                                        if (highlightToggle.checked) {
                                            document.querySelectorAll('.paper-card').forEach(card => {
                                                card.style.transform = '';
                                                card.style.opacity = '';
                                            });
                                        }
                                    });

                                    container.appendChild(element);
                                });

                                logger.log('INFO', 'Word cloud rendering completed successfully');
                            } catch (error) {
                                logger.log('ERROR', 'Error in draw function', {
                                    error: error.message,
                                    stack: error.stack
                                });
                            }
                        }

                    } catch (error) {
                        logger.log('ERROR', 'Error rendering word cloud', {
                            error: error.message,
                            stack: error.stack
                        });

                        // Show error message in container
                        const container = document.getElementById('word-cloud');
                        if (container) {
                            container.innerHTML = `<div class="text-center text-red-500 p-8">Error rendering word cloud: ${error.message}</div>`;
                        }
                    }
                }

                function renderPapersList() {
                    logger.log('INFO', 'Rendering papers list', { paperCount: papers.length });

                    try {
                        papersList.innerHTML = '';

                        papers.forEach(paper => {
                            const card = document.createElement('div');
                            card.className = 'paper-card bg-white rounded-lg shadow-md overflow-hidden h-full flex flex-col';
                            card.dataset.paperId = paper.id;

                            card.innerHTML = `
                            <div class="p-6 flex-grow">
                                <div class="flex justify-between items-start mb-2">
                                    <h3 class="text-xl font-semibold text-gray-800 mb-2">${paper.title}</h3>
                                    <span class="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
                                        ${paper.date}
                                    </span>
                                </div>
                                <p class="text-gray-600 text-sm mb-4 line-clamp-3">${paper.abstract}</p>
                                <div class="text-sm text-gray-500 mb-4">By ${paper.authors}</div>
                                <div class="flex flex-wrap gap-2 mb-4">
                                    ${paper.tags.map(tag => `
                                        <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${tag}</span>
                                    `).join('')}
                                </div>
                            </div>
                            <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                                <a href="${paper.url}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium" target="_blank">
                                    <i class="fas fa-external-link-alt mr-1"></i> Read Paper
                                </a>
                                <button class="text-gray-500 hover:text-gray-700">
                                    <i class="far fa-bookmark"></i>
                                </button>
                            </div>
                        `;

                            papersList.appendChild(card);
                        });

                        logger.log('INFO', 'Papers list rendered successfully');
                    } catch (error) {
                        logger.log('ERROR', 'Error rendering papers list', {
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }

                function filterPapers() {
                    const searchTerm = paperSearch.value.toLowerCase();
                    logger.log('DEBUG', 'Filtering papers', { searchTerm });

                    try {
                        const cards = document.querySelectorAll('.paper-card');
                        let visibleCount = 0;

                        cards.forEach(card => {
                            const paperId = card.dataset.paperId;
                            const paper = papers.find(p => p.id === paperId);

                            const matches = searchTerm === '' ||
                                paper.title.toLowerCase().includes(searchTerm) ||
                                paper.abstract.toLowerCase().includes(searchTerm) ||
                                paper.authors.toLowerCase().includes(searchTerm) ||
                                paper.tags.some(tag => tag.toLowerCase().includes(searchTerm));

                            card.style.display = matches ? '' : 'none';
                            if (matches) visibleCount++;
                        });

                        logger.log('INFO', 'Papers filtered', {
                            searchTerm,
                            totalCards: cards.length,
                            visibleCount
                        });
                    } catch (error) {
                        logger.log('ERROR', 'Error filtering papers', {
                            error: error.message,
                            stack: error.stack
                        });
                    }
                }

                // Global error handler
                window.addEventListener('error', (event) => {
                    logger.log('ERROR', 'Global error caught', {
                        message: event.message,
                        filename: event.filename,
                        lineno: event.lineno,
                        colno: event.colno,
                        error: event.error?.stack
                    });
                });

                window.addEventListener('unhandledrejection', (event) => {
                    logger.log('ERROR', 'Unhandled promise rejection', {
                        reason: event.reason,
                        promise: event.promise
                    });
                });

                logger.log('INFO', 'Application initialization completed');

            } catch (error) {
                logger.log('ERROR', 'Critical error during initialization', {
                    error: error.message,
                    stack: error.stack
                });

                // Show critical error to user
                document.body.innerHTML = `
                    <div class="min-h-screen flex items-center justify-center bg-red-50">
                        <div class="text-center">
                            <h1 class="text-2xl font-bold text-red-600 mb-4">Application Error</h1>
                            <p class="text-red-500 mb-4">A critical error occurred during initialization:</p>
                            <p class="text-sm text-gray-600 bg-white p-4 rounded border">${error.message}</p>
                            <button onclick="location.reload()" class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700">
                                Reload Page
                            </button>
                        </div>
                    </div>
                `;
            }
        });
    </script>
</body>

</html>