<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hugging Face Papers Word Cloud</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .word-cloud-container {
            width: 100%;
            height: 70vh;
            margin: 0 auto;
            position: relative;
            overflow: visible;
        }

        .word {
            position: absolute;
            cursor: pointer;
            transition: all 0.3s ease;
            transform-origin: center center;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        }

        .word:hover {
            transform: scale(1.2);
            z-index: 100;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
        }

        @keyframes float {

            0%,
            100% {
                transform: translateY(0);
            }

            50% {
                transform: translateY(-10px);
            }
        }

        .floating {
            animation: float 3s ease-in-out infinite;
        }

        .paper-card {
            transition: all 0.3s ease;
            transform-origin: top left;
        }

        .paper-card:hover {
            transform: scale(1.02);
            box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
    </style>
</head>

<body class="bg-gray-50 min-h-screen">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <!-- Header -->
        <header class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-indigo-700 mb-4">
                <i class="fas fa-cloud-word mr-2"></i> Hugging Face Papers Word Cloud
            </h1>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Visualizing research trends from today's AI papers on Hugging Face
            </p>
            <div class="flex justify-center mt-6">
                <button id="refresh-btn"
                    class="flex items-center bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-lg transition-all duration-300">
                    <i class="fas fa-sync-alt mr-2"></i> Refresh Papers
                </button>
            </div>
        </header>

        <!-- Loading State -->
        <div id="loading" class="text-center py-20">
            <div
                class="inline-block animate-spin rounded-full h-16 w-16 border-t-4 border-indigo-500 border-opacity-50 mb-4">
            </div>
            <p class="text-xl text-gray-600">Fetching today's papers from Hugging Face...</p>
        </div>

        <!-- Word Cloud Section -->
        <div id="word-cloud-section" class="hidden mb-16">
            <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-semibold text-gray-800">
                            <i class="fas fa-cloud text-indigo-500 mr-2"></i> Trending Topics
                        </h2>
                        <div class="flex items-center">
                            <span class="text-sm text-gray-500 mr-2">Highlight Mode:</span>
                            <div class="relative inline-block w-12 mr-2 align-middle select-none">
                                <input type="checkbox" id="highlight-toggle" class="sr-only" checked>
                                <label for="highlight-toggle"
                                    class="block overflow-hidden h-6 rounded-full bg-gray-300 cursor-pointer">
                                    <span
                                        class="toggle-checkbox block h-6 w-6 rounded-full bg-white shadow-md transform transition ease-in-out duration-200"></span>
                                </label>
                            </div>
                            <select id="color-scheme" class="border rounded px-2 py-1 text-sm bg-white">
                                <option value="default">Default Colors</option>
                                <option value="bright">Bright Colors</option>
                                <option value="pastel">Pastel Colors</option>
                                <option value="monochrome">Monochrome</option>
                            </select>
                        </div>
                    </div>
                    <div class="word-cloud-container bg-gray-50 rounded-lg" id="word-cloud"></div>
                </div>
            </div>

            <!-- Papers List -->
            <div id="papers-container">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-semibold text-gray-800">
                        <i class="fas fa-file-alt text-indigo-500 mr-2"></i> Today's Papers
                    </h2>
                    <div class="relative">
                        <input type="text" id="paper-search" placeholder="Search papers..."
                            class="pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500">
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
                    </div>
                </div>
                <div id="papers-list" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"></div>
            </div>
        </div>

        <!-- No Papers State -->
        <div id="no-papers" class="hidden text-center py-20">
            <i class="fas fa-cloud-showers-heavy text-6xl text-gray-400 mb-6"></i>
            <h3 class="text-2xl font-semibold text-gray-700 mb-2">No Papers Found Today</h3>
            <p class="text-gray-500 max-w-lg mx-auto">
                We couldn't find any recent papers on Hugging Face. Try refreshing later or check your connection.
            </p>
        </div>
    </div>

    <!-- Footer -->
    <footer class="bg-white py-8 border-t border-gray-200 mt-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <p class="text-gray-500">
                    Made with <i class="fas fa-heart text-red-500"></i> using Hugging Face API
                </p>
                <p class="text-gray-400 text-sm mt-2">
                    Data updates in real-time. Not affiliated with Hugging Face.
                </p>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // UI Elements
            const wordCloudSection = document.getElementById('word-cloud-section');
            const loadingElement = document.getElementById('loading');
            const noPapersElement = document.getElementById('no-papers');
            const papersList = document.getElementById('papers-list');
            const refreshBtn = document.getElementById('refresh-btn');
            const paperSearch = document.getElementById('paper-search');
            const highlightToggle = document.getElementById('highlight-toggle');
            const colorSchemeSelect = document.getElementById('color-scheme');

            // State
            let papers = [];
            let wordData = [];
            let colorScheme = 'default';

            // Initialize
            fetchPapers();

            // Event Listeners
            refreshBtn.addEventListener('click', fetchPapers);
            paperSearch.addEventListener('input', filterPapers);
            colorSchemeSelect.addEventListener('change', function () {
                colorScheme = this.value;
                renderWordCloud();
            });
            highlightToggle.addEventListener('change', function () {
                document.getElementById('word-cloud').classList.toggle('highlight-mode');
                renderWordCloud();
            });

            // Functions
            async function fetchPapers() {
                try {
                    // Show loading state
                    wordCloudSection.classList.add('hidden');
                    noPapersElement.classList.add('hidden');
                    loadingElement.classList.remove('hidden');

                    // Simulate fetching papers from Hugging Face (in a real app, you would use their API)
                    // This is a mock implementation for demonstration
                    papers = await mockFetchPapers();

                    if (papers.length > 0) {
                        // Process papers to extract words
                        wordData = processPapersForWordCloud(papers);

                        // Render UI
                        renderWordCloud();
                        renderPapersList();

                        wordCloudSection.classList.remove('hidden');
                        noPapersElement.classList.add('hidden');
                    } else {
                        wordCloudSection.classList.add('hidden');
                        noPapersElement.classList.remove('hidden');
                    }

                    loadingElement.classList.add('hidden');
                } catch (error) {
                    console.error('Error fetching papers:', error);
                    loadingElement.classList.add('hidden');
                    noPapersElement.classList.remove('hidden');
                }
            }

            function mockFetchPapers() {
                // In a real implementation, you would fetch from Hugging Face API
                return new Promise(resolve => {
                    setTimeout(() => {
                        // Mock papers data with more fields and realistic content
                        const mockPapers = [
                            {
                                id: '1',
                                title: 'Transformer Models for Multilingual Machine Translation',
                                abstract: 'We present a comprehensive study of transformer architectures applied to machine translation across 100 languages. Our results show significant improvements in low-resource language pairs.',
                                authors: 'Smith et al.',
                                date: new Date().toISOString().split('T')[0],
                                url: '#',
                                tags: ['transformer', 'machine translation', 'multilingual', 'low-resource', 'NLP']
                            },
                            {
                                id: '2',
                                title: 'Efficient Fine-Tuning of Large Language Models',
                                abstract: 'This paper introduces a novel parameter-efficient fine-tuning method that achieves comparable performance to full fine-tuning while using only 1% of the parameters.',
                                authors: 'Johnson et al.',
                                date: new Date().toISOString().split('T')[0],
                                url: '#',
                                tags: ['LLM', 'fine-tuning', 'efficiency', 'parameter-efficient', 'adapters']
                            },
                            {
                                id: '3',
                                title: 'Contrastive Learning for Self-Supervised Speech Representation',
                                abstract: 'We propose a contrastive learning framework for learning speech representations without transcriptions. The model achieves state-of-the-art results on multiple speech tasks.',
                                authors: 'Lee et al.',
                                date: new Date().toISOString().split('T')[0],
                                url: '#',
                                tags: ['speech', 'self-supervised', 'contrastive learning', 'representation learning', 'audio']
                            },
                            {
                                id: '4',
                                title: 'Diffusion Models for High-Quality Image Generation',
                                abstract: 'A new class of diffusion models that can generate high-resolution images with unprecedented quality. The method shows particular strength in compositional generation.',
                                authors: 'Chen et al.',
                                date: new Date().toISOString().split('T')[0],
                                url: '#',
                                tags: ['diffusion', 'image generation', 'GAN', 'computer vision', 'generative models']
                            },
                            {
                                id: '5',
                                title: 'Ethical Considerations in Large Language Model Deployment',
                                abstract: 'We examine the ethical challenges and potential mitigations for deploying large language models in production systems, with a focus on bias and misuse.',
                                authors: 'Ethics Research Team',
                                date: new Date().toISOString().split('T')[0],
                                url: '#',
                                tags: ['ethics', 'LLM', 'bias', 'responsible AI', 'deployment']
                            },
                            {
                                id: '6',
                                title: 'Neural Architecture Search for Edge Devices',
                                abstract: 'We present an evolutionary NAS approach that discovers architectures optimized for inference on resource-constrained edge devices.',
                                authors: 'Wang et al.',
                                date: new Date().toISOString().split('T')[0],
                                url: '#',
                                tags: ['NAS', 'edge computing', 'efficiency', 'architecture search', 'compression']
                            }
                        ];
                        resolve(mockPapers);
                    }, 1500); // Simulate network delay
                });
            }

            function processPapersForWordCloud(papers) {
                // Combine all text fields from papers
                const allText = papers.map(paper =>
                    `${paper.title} ${paper.abstract} ${paper.tags.join(' ')}`
                ).join(' ');

                // Common words to exclude
                const stopWords = new Set(['the', 'and', 'for', 'with', 'this', 'that', 'these', 'those', 'are', 'was', 'were', 'have', 'has', 'had', 'a', 'an', 'in', 'on', 'at', 'to', 'of', 'by', 'we', 'our', 'as', 'be', 'is', 'can', 'how', 'what', 'which', 'it', 'its', 'from']);

                // Extract words and count frequencies
                const wordCounts = {};
                const words = allText.toLowerCase().match(/\b[\w'-]+\b/g) || [];

                words.forEach(word => {
                    if (!stopWords.has(word) && word.length > 2) {
                        wordCounts[word] = (wordCounts[word] || 0) + 1;
                    }
                });

                // Convert to array and sort by frequency
                return Object.entries(wordCounts)
                    .map(([text, value]) => ({ text, value }))
                    .sort((a, b) => b.value - a.value)
                    .slice(0, 50); // Limit to top 50 words
            }

            function renderWordCloud() {
                const container = document.getElementById('word-cloud');
                container.innerHTML = '';

                const width = container.clientWidth;
                const height = container.clientHeight;

                // Color schemes
                const colors = {
                    default: ['#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6', '#ec4899'],
                    bright: ['#FF5733', '#33FF57', '#3357FF', '#FF33A8', '#33FFF5', '#F5FF33'],
                    pastel: ['#A5D6A7', '#81D4FA', '#CE93D8', '#FFF59D', '#FFAB91', '#90CAF9'],
                    monochrome: ['#4a5568', '#718096', '#a0aec0', '#cbd5e0', '#e2e8f0']
                };

                // Get selected color scheme
                const currentColors = colors[colorScheme];

                // Generate positions using D3 layout
                const layout = d3.layout.cloud()
                    .size([width, height])
                    .words(wordData)
                    .padding(5)
                    .rotate(() => (Math.random() > 0.5 ? 0 : 90))
                    .fontSize(d => Math.min(Math.max(14, d.value * 5), 50))
                    .on('end', draw);

                layout.start();

                function draw(words) {
                    words.forEach((word, i) => {
                        const element = document.createElement('div');
                        element.className = 'word';
                        element.style.fontSize = `${word.size}px`;
                        element.style.color = currentColors[i % currentColors.length];
                        element.style.left = `${word.x + width / 2 - word.size / 2}px`;
                        element.style.top = `${word.y + height / 2 - word.size / 2}px`;
                        element.style.opacity = word.value / Math.max(...wordData.map(w => w.value)) * 0.8 + 0.2;
                        element.style.fontWeight = 100 + (word.value * 10);
                        element.textContent = word.text;

                        // Add some random floating animation to some words
                        if (Math.random() > 0.7) {
                            element.classList.add('floating');
                            element.style.animationDelay = `${Math.random() * 3}s`;
                        }

                        // Highlight papers containing this word on hover
                        element.addEventListener('mouseenter', () => {
                            if (highlightToggle.checked) {
                                const papersWithWord = papers.filter(paper =>
                                    paper.title.toLowerCase().includes(word.text) ||
                                    paper.abstract.toLowerCase().includes(word.text) ||
                                    paper.tags.includes(word.text)
                                );

                                document.querySelectorAll('.paper-card').forEach(card => {
                                    const paperId = card.dataset.paperId;
                                    const shouldHighlight = papersWithWord.some(p => p.id === paperId);
                                    card.style.transform = shouldHighlight ? 'scale(1.05)' : 'scale(0.95)';
                                    card.style.opacity = shouldHighlight ? '1' : '0.6';
                                });
                            }
                        });

                        element.addEventListener('mouseleave', () => {
                            if (highlightToggle.checked) {
                                document.querySelectorAll('.paper-card').forEach(card => {
                                    card.style.transform = '';
                                    card.style.opacity = '';
                                });
                            }
                        });

                        container.appendChild(element);
                    });
                }
            }

            function renderPapersList() {
                papersList.innerHTML = '';

                papers.forEach(paper => {
                    const card = document.createElement('div');
                    card.className = 'paper-card bg-white rounded-lg shadow-md overflow-hidden h-full flex flex-col';
                    card.dataset.paperId = paper.id;

                    card.innerHTML = `
                        <div class="p-6 flex-grow">
                            <div class="flex justify-between items-start mb-2">
                                <h3 class="text-xl font-semibold text-gray-800 mb-2">${paper.title}</h3>
                                <span class="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
                                    ${paper.date}
                                </span>
                            </div>
                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">${paper.abstract}</p>
                            <div class="text-sm text-gray-500 mb-4">By ${paper.authors}</div>
                            <div class="flex flex-wrap gap-2 mb-4">
                                ${paper.tags.map(tag => `
                                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${tag}</span>
                                `).join('')}
                            </div>
                        </div>
                        <div class="px-6 py-3 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
                            <a href="${paper.url}" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium" target="_blank">
                                <i class="fas fa-external-link-alt mr-1"></i> Read Paper
                            </a>
                            <button class="text-gray-500 hover:text-gray-700">
                                <i class="far fa-bookmark"></i>
                            </button>
                        </div>
                    `;

                    papersList.appendChild(card);
                });
            }

            function filterPapers() {
                const searchTerm = paperSearch.value.toLowerCase();
                const cards = document.querySelectorAll('.paper-card');

                cards.forEach(card => {
                    const paperId = card.dataset.paperId;
                    const paper = papers.find(p => p.id === paperId);

                    const matches = searchTerm === '' ||
                        paper.title.toLowerCase().includes(searchTerm) ||
                        paper.abstract.toLowerCase().includes(searchTerm) ||
                        paper.authors.toLowerCase().includes(searchTerm) ||
                        paper.tags.some(tag => tag.toLowerCase().includes(searchTerm));

                    card.style.display = matches ? '' : 'none';
                });
            }
        });
    </script>
</body>

</html>