{"timestamp": "2025-06-05T18:39:31.044Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:39:38.245Z"}, {"name": "Loading Element Visible", "passed": false, "timestamp": "2025-06-05T18:39:39.462Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:39:39.845Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:39:39.976Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:39:43.476Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:39:44.495Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:39:46.439Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:39:46.845Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:39:34.737Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:39:35.174Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:39:35.393Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:39:35.394Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:39:35.395Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:39:35.408Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:39:35.409Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:39:35.409Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:39:35.412Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:39:35.420Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:39:35.422Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:39:35.423Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:39:35.423Z"}, {"type": "error", "text": "Failed to load resource: the server responded with a status of 401 ()", "timestamp": "2025-06-05T18:39:35.813Z"}, {"type": "error", "text": "[ERROR] Failed to fetch from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:39:35.825Z"}, {"type": "log", "text": "[INFO] Falling back to curated papers due to API error JSHandle@object", "timestamp": "2025-06-05T18:39:35.826Z"}, {"type": "log", "text": "[INFO] Using curated papers as fallback JSHandle@object", "timestamp": "2025-06-05T18:39:35.826Z"}, {"type": "log", "text": "[INFO] Curated papers generated JSHandle@object", "timestamp": "2025-06-05T18:39:35.826Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:39:35.826Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:39:35.827Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:39:35.828Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:35.828Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:35.829Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:39:35.829Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:39:35.836Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:39:35.837Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:39:35.838Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:39:35.838Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:39:35.838Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:39:35.839Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:39:35.839Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:39:35.839Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:39:35.839Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:39:35.840Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:39:35.993Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:39:35.993Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:35.994Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:39:35.996Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.360Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.363Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.363Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.448Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.470Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.471Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.471Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.472Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.472Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.473Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.473Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.473Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.474Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.474Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.474Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.474Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.474Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.475Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.475Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.475Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.475Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.475Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.475Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.476Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.476Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.476Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.476Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.476Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.477Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.477Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.477Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.478Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.478Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.478Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.478Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.478Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.478Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.479Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.480Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.481Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.481Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.481Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.482Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:36.487Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:39:36.488Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:39:36.490Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:39:42.360Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:39:42.364Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:39:42.365Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:39:42.377Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:39:42.378Z"}, {"type": "error", "text": "Failed to load resource: the server responded with a status of 401 ()", "timestamp": "2025-06-05T18:39:42.604Z"}, {"type": "error", "text": "[ERROR] Failed to fetch from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:39:42.610Z"}, {"type": "log", "text": "[INFO] Falling back to curated papers due to API error JSHandle@object", "timestamp": "2025-06-05T18:39:42.614Z"}, {"type": "log", "text": "[INFO] Using curated papers as fallback JSHandle@object", "timestamp": "2025-06-05T18:39:42.625Z"}, {"type": "log", "text": "[INFO] Curated papers generated JSHandle@object", "timestamp": "2025-06-05T18:39:42.630Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:39:42.647Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:39:42.659Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:39:42.659Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:42.678Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:42.679Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:39:42.679Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:39:42.680Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:39:42.689Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:39:42.776Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:39:42.777Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:39:42.777Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:39:42.777Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:39:42.779Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:39:42.797Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:39:43.024Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:39:43.043Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:39:43.061Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:39:43.062Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:43.062Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:39:43.062Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.062Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.062Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.074Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.077Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.077Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.077Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.080Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.080Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.081Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.081Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.081Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.081Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.081Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.088Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.089Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.090Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.090Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:39:43.091Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:39:43.091Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:39:43.092Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:39:43.872Z"}, {"type": "log", "text": "[DEBUG] Updating word cloud colors JSHandle@object", "timestamp": "2025-06-05T18:39:43.955Z"}, {"type": "log", "text": "[INFO] Word cloud colors updated JSHandle@object", "timestamp": "2025-06-05T18:39:43.963Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.198Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.209Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.209Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.244Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.245Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.245Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.288Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.289Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.290Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.341Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.342Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.342Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.413Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.427Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.428Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.541Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.542Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.543Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.576Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.579Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.580Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.609Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.611Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.612Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.629Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.630Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.630Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.659Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.697Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.698Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:39:45.795Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:39:45.797Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:39:45.798Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749148776997.png", "timestamp": "2025-06-05T18:39:38.088Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749148780363.png", "timestamp": "2025-06-05T18:39:41.229Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749148786909.png", "timestamp": "2025-06-05T18:39:47.327Z"}], "errors": [], "performance": {"pageLoad": 3473}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:39:35.170Z", "elapsed": 2, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:39:35.169Z"}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.327Z", "elapsed": 159, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.329Z", "elapsed": 161, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.329Z", "elapsed": 161, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.329Z", "elapsed": 161, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.330Z", "elapsed": 162, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.331Z", "elapsed": 163, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.331Z", "elapsed": 163, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.333Z", "elapsed": 165, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.336Z", "elapsed": 168, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.339Z", "elapsed": 171, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.341Z", "elapsed": 173, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.815Z", "elapsed": 647, "level": "ERROR", "message": "Failed to fetch from Hugging Face API", "data": {"error": "HTTP error! status: 401", "stack": "Error: HTTP error! status: 401\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:476:35)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:218:48)\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:526:32)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, {"timestamp": "2025-06-05T18:39:35.820Z", "elapsed": 652, "level": "INFO", "message": "Falling back to curated papers due to API error", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.821Z", "elapsed": 653, "level": "INFO", "message": "Using curated papers as fallback", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.822Z", "elapsed": 654, "level": "INFO", "message": "Curated papers generated", "data": {"paperCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.823Z", "elapsed": 655, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 10, "fetchTimeMs": 492}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.823Z", "elapsed": 655, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.824Z", "elapsed": 656, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.825Z", "elapsed": 657, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 3048}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.825Z", "elapsed": 657, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 403}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.826Z", "elapsed": 658, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 169, "topWords": 50, "mostFrequent": "models"}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.827Z", "elapsed": 659, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 3}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.827Z", "elapsed": 659, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 10, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.828Z", "elapsed": 660, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.829Z", "elapsed": 661, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.830Z", "elapsed": 662, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.831Z", "elapsed": 663, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.832Z", "elapsed": 664, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.838Z", "elapsed": 670, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.838Z", "elapsed": 670, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.838Z", "elapsed": 670, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 508}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.977Z", "elapsed": 809, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.978Z", "elapsed": 810, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.978Z", "elapsed": 810, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:39:35.978Z", "elapsed": 810, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.225Z", "elapsed": 1057, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 801, "y": 222}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.228Z", "elapsed": 1060, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 668, "y": 306}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.231Z", "elapsed": 1063, "level": "DEBUG", "message": "Word positioned", "data": {"text": "vision", "x": 683, "y": 161}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.237Z", "elapsed": 1069, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 573, "y": 255}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.239Z", "elapsed": 1071, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 447, "y": 374}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.240Z", "elapsed": 1072, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 439, "y": 129}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.242Z", "elapsed": 1074, "level": "DEBUG", "message": "Word positioned", "data": {"text": "image", "x": 778, "y": 257}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.243Z", "elapsed": 1075, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 360, "y": 262}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.247Z", "elapsed": 1079, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 751, "y": 125}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.248Z", "elapsed": 1080, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nlp", "x": 675, "y": 268}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.254Z", "elapsed": 1086, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deep", "x": 545, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.255Z", "elapsed": 1087, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformers", "x": 478, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.256Z", "elapsed": 1088, "level": "DEBUG", "message": "Word positioned", "data": {"text": "computer", "x": 571, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.256Z", "elapsed": 1088, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 393, "y": 323}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.257Z", "elapsed": 1089, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bert", "x": 410, "y": 375}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.258Z", "elapsed": 1090, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 709, "y": 110}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.259Z", "elapsed": 1091, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generative", "x": 646, "y": 340}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.260Z", "elapsed": 1092, "level": "DEBUG", "message": "Word positioned", "data": {"text": "recognition", "x": 627, "y": 119}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.261Z", "elapsed": 1093, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 747, "y": 315}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.261Z", "elapsed": 1093, "level": "DEBUG", "message": "Word positioned", "data": {"text": "pre-training", "x": 867, "y": 171}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.262Z", "elapsed": 1094, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bidirectional", "x": 817, "y": 285}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.262Z", "elapsed": 1094, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representations", "x": 890, "y": 242}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.263Z", "elapsed": 1095, "level": "DEBUG", "message": "Word positioned", "data": {"text": "adversarial", "x": 695, "y": 182}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.264Z", "elapsed": 1096, "level": "DEBUG", "message": "Word positioned", "data": {"text": "present", "x": 886, "y": 273}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.264Z", "elapsed": 1096, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 341, "y": 353}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.265Z", "elapsed": 1097, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 486, "y": 318}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.268Z", "elapsed": 1100, "level": "DEBUG", "message": "Word positioned", "data": {"text": "probabilistic", "x": 500, "y": 224}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.269Z", "elapsed": 1101, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mobilenets", "x": 586, "y": 185}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.270Z", "elapsed": 1102, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficient", "x": 399, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.271Z", "elapsed": 1103, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mobile", "x": 412, "y": 232}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.271Z", "elapsed": 1103, "level": "DEBUG", "message": "Word positioned", "data": {"text": "sequence", "x": 502, "y": 365}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.271Z", "elapsed": 1103, "level": "DEBUG", "message": "Word positioned", "data": {"text": "based", "x": 303, "y": 193}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.272Z", "elapsed": 1104, "level": "DEBUG", "message": "Word positioned", "data": {"text": "convolutional", "x": 521, "y": 170}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.273Z", "elapsed": 1105, "level": "DEBUG", "message": "Word positioned", "data": {"text": "encoder", "x": 312, "y": 233}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.274Z", "elapsed": 1106, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mechanism", "x": 927, "y": 286}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.278Z", "elapsed": 1110, "level": "DEBUG", "message": "Word positioned", "data": {"text": "new", "x": 710, "y": 324}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.279Z", "elapsed": 1111, "level": "DEBUG", "message": "Word positioned", "data": {"text": "called", "x": 334, "y": 263}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.280Z", "elapsed": 1112, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 832, "y": 144}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.282Z", "elapsed": 1114, "level": "DEBUG", "message": "Word positioned", "data": {"text": "train", "x": 518, "y": 321}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.287Z", "elapsed": 1119, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 798, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.288Z", "elapsed": 1120, "level": "DEBUG", "message": "Word positioned", "data": {"text": "residual", "x": 338, "y": 210}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.288Z", "elapsed": 1120, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deeper", "x": 356, "y": 161}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.289Z", "elapsed": 1121, "level": "DEBUG", "message": "Word positioned", "data": {"text": "few-shot", "x": 651, "y": 220}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.291Z", "elapsed": 1123, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 433, "y": 204}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.292Z", "elapsed": 1124, "level": "DEBUG", "message": "Word positioned", "data": {"text": "openai", "x": 546, "y": 346}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.294Z", "elapsed": 1126, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 843, "y": 338}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.295Z", "elapsed": 1127, "level": "DEBUG", "message": "Word positioned", "data": {"text": "natural", "x": 699, "y": 222}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.297Z", "elapsed": 1130, "level": "DEBUG", "message": "Word positioned", "data": {"text": "clip", "x": 486, "y": 194}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.303Z", "elapsed": 1135, "level": "DEBUG", "message": "Word positioned", "data": {"text": "supervision", "x": 924, "y": 160}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.305Z", "elapsed": 1138, "level": "DEBUG", "message": "Word positioned", "data": {"text": "denoising", "x": 559, "y": 125}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.307Z", "elapsed": 1139, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:36.311Z", "elapsed": 1143, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.348Z", "elapsed": 7180, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.352Z", "elapsed": 7184, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.354Z", "elapsed": 7186, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.354Z", "elapsed": 7186, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.355Z", "elapsed": 7187, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.529Z", "elapsed": 7361, "level": "ERROR", "message": "Failed to fetch from Hugging Face API", "data": {"error": "HTTP error! status: 401", "stack": "Error: HTTP error! status: 401\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:476:35)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:218:48)\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:526:32)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, {"timestamp": "2025-06-05T18:39:42.530Z", "elapsed": 7362, "level": "INFO", "message": "Falling back to curated papers due to API error", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.531Z", "elapsed": 7363, "level": "INFO", "message": "Using curated papers as fallback", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.531Z", "elapsed": 7363, "level": "INFO", "message": "Curated papers generated", "data": {"paperCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.541Z", "elapsed": 7373, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 10, "fetchTimeMs": 189}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.542Z", "elapsed": 7374, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.544Z", "elapsed": 7376, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.544Z", "elapsed": 7376, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 3048}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.545Z", "elapsed": 7377, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 403}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.547Z", "elapsed": 7379, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 169, "topWords": 50, "mostFrequent": "models"}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.548Z", "elapsed": 7380, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 4}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.558Z", "elapsed": 7390, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 10, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.559Z", "elapsed": 7391, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.562Z", "elapsed": 7394, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.564Z", "elapsed": 7396, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.565Z", "elapsed": 7397, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.574Z", "elapsed": 7406, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.576Z", "elapsed": 7408, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.577Z", "elapsed": 7409, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 15}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.579Z", "elapsed": 7411, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 227}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.679Z", "elapsed": 7511, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.681Z", "elapsed": 7513, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.687Z", "elapsed": 7519, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.688Z", "elapsed": 7520, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.874Z", "elapsed": 7706, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 680, "y": 219}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.875Z", "elapsed": 7707, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 572, "y": 163}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.878Z", "elapsed": 7710, "level": "DEBUG", "message": "Word positioned", "data": {"text": "vision", "x": 754, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.879Z", "elapsed": 7711, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 861, "y": 319}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.888Z", "elapsed": 7720, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 725, "y": 159}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.889Z", "elapsed": 7721, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 478, "y": 244}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.891Z", "elapsed": 7723, "level": "DEBUG", "message": "Word positioned", "data": {"text": "image", "x": 498, "y": 175}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.893Z", "elapsed": 7725, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 897, "y": 352}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.895Z", "elapsed": 7727, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 535, "y": 248}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.896Z", "elapsed": 7729, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nlp", "x": 713, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.898Z", "elapsed": 7730, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deep", "x": 662, "y": 134}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.904Z", "elapsed": 7736, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformers", "x": 842, "y": 226}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.906Z", "elapsed": 7738, "level": "DEBUG", "message": "Word positioned", "data": {"text": "computer", "x": 506, "y": 136}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.907Z", "elapsed": 7739, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 836, "y": 198}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.909Z", "elapsed": 7741, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bert", "x": 592, "y": 239}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.910Z", "elapsed": 7742, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 785, "y": 272}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.911Z", "elapsed": 7743, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generative", "x": 673, "y": 342}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.912Z", "elapsed": 7744, "level": "DEBUG", "message": "Word positioned", "data": {"text": "recognition", "x": 643, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.914Z", "elapsed": 7746, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 934, "y": 252}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.915Z", "elapsed": 7747, "level": "DEBUG", "message": "Word positioned", "data": {"text": "pre-training", "x": 800, "y": 367}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.919Z", "elapsed": 7751, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bidirectional", "x": 415, "y": 303}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.921Z", "elapsed": 7754, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representations", "x": 814, "y": 167}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.922Z", "elapsed": 7754, "level": "DEBUG", "message": "Word positioned", "data": {"text": "adversarial", "x": 384, "y": 202}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.923Z", "elapsed": 7755, "level": "DEBUG", "message": "Word positioned", "data": {"text": "present", "x": 865, "y": 266}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.924Z", "elapsed": 7756, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 434, "y": 171}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.925Z", "elapsed": 7757, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 641, "y": 262}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.926Z", "elapsed": 7758, "level": "DEBUG", "message": "Word positioned", "data": {"text": "probabilistic", "x": 587, "y": 326}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.927Z", "elapsed": 7759, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mobilenets", "x": 437, "y": 249}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.929Z", "elapsed": 7761, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficient", "x": 354, "y": 213}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.931Z", "elapsed": 7763, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mobile", "x": 797, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.940Z", "elapsed": 7772, "level": "DEBUG", "message": "Word positioned", "data": {"text": "sequence", "x": 820, "y": 262}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.941Z", "elapsed": 7773, "level": "DEBUG", "message": "Word positioned", "data": {"text": "based", "x": 587, "y": 350}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.943Z", "elapsed": 7775, "level": "DEBUG", "message": "Word positioned", "data": {"text": "convolutional", "x": 649, "y": 205}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.944Z", "elapsed": 7776, "level": "DEBUG", "message": "Word positioned", "data": {"text": "encoder", "x": 910, "y": 200}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.946Z", "elapsed": 7778, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mechanism", "x": 516, "y": 103}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.948Z", "elapsed": 7780, "level": "DEBUG", "message": "Word positioned", "data": {"text": "new", "x": 751, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.956Z", "elapsed": 7788, "level": "DEBUG", "message": "Word positioned", "data": {"text": "called", "x": 376, "y": 161}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.957Z", "elapsed": 7789, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 383, "y": 255}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.957Z", "elapsed": 7789, "level": "DEBUG", "message": "Word positioned", "data": {"text": "train", "x": 760, "y": 247}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.958Z", "elapsed": 7790, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 345, "y": 300}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.959Z", "elapsed": 7791, "level": "DEBUG", "message": "Word positioned", "data": {"text": "residual", "x": 461, "y": 320}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.960Z", "elapsed": 7792, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deeper", "x": 593, "y": 295}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.961Z", "elapsed": 7793, "level": "DEBUG", "message": "Word positioned", "data": {"text": "few-shot", "x": 616, "y": 140}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.962Z", "elapsed": 7794, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 730, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.963Z", "elapsed": 7795, "level": "DEBUG", "message": "Word positioned", "data": {"text": "openai", "x": 514, "y": 315}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.964Z", "elapsed": 7796, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 300, "y": 260}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.971Z", "elapsed": 7803, "level": "DEBUG", "message": "Word positioned", "data": {"text": "natural", "x": 621, "y": 198}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.974Z", "elapsed": 7806, "level": "DEBUG", "message": "Word positioned", "data": {"text": "clip", "x": 445, "y": 351}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.975Z", "elapsed": 7807, "level": "DEBUG", "message": "Word positioned", "data": {"text": "supervision", "x": 367, "y": 339}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.976Z", "elapsed": 7808, "level": "DEBUG", "message": "Word positioned", "data": {"text": "denoising", "x": 878, "y": 143}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.978Z", "elapsed": 7810, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.980Z", "elapsed": 7812, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:43.864Z", "elapsed": 8696, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:39:43.870Z", "elapsed": 8702, "level": "DEBUG", "message": "Updating word cloud colors", "data": {"colorScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:39:43.872Z", "elapsed": 8704, "level": "INFO", "message": "Word cloud colors updated", "data": {"colorScheme": "bright", "elementsUpdated": 50}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.188Z", "elapsed": 10020, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.190Z", "elapsed": 10022, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.191Z", "elapsed": 10023, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 10, "visibleCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.218Z", "elapsed": 10050, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.219Z", "elapsed": 10051, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.221Z", "elapsed": 10053, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 10, "visibleCount": 9}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.276Z", "elapsed": 10108, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.277Z", "elapsed": 10109, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.279Z", "elapsed": 10111, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 10, "visibleCount": 8}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.299Z", "elapsed": 10131, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.303Z", "elapsed": 10135, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.304Z", "elapsed": 10137, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 10, "visibleCount": 5}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.348Z", "elapsed": 10180, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.354Z", "elapsed": 10186, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.356Z", "elapsed": 10188, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 10, "visibleCount": 5}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.514Z", "elapsed": 10346, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.517Z", "elapsed": 10349, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.519Z", "elapsed": 10351, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 10, "visibleCount": 4}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.557Z", "elapsed": 10389, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.558Z", "elapsed": 10390, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.559Z", "elapsed": 10391, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 10, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.600Z", "elapsed": 10432, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.603Z", "elapsed": 10435, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.605Z", "elapsed": 10437, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 10, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.627Z", "elapsed": 10459, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.628Z", "elapsed": 10460, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.628Z", "elapsed": 10460, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 10, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.653Z", "elapsed": 10485, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.654Z", "elapsed": 10486, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.655Z", "elapsed": 10487, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 10, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.729Z", "elapsed": 10561, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.731Z", "elapsed": 10563, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:39:45.735Z", "elapsed": 10567, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 10, "visibleCount": 3}, "stack": null}], "stats": {"totalLogs": 205, "byLevel": {"DEBUG": 139, "INFO": 62, "WARN": 2, "ERROR": 2}, "errors": [{"timestamp": "2025-06-05T18:39:35.815Z", "elapsed": 647, "level": "ERROR", "message": "Failed to fetch from Hugging Face API", "data": {"error": "HTTP error! status: 401", "stack": "Error: HTTP error! status: 401\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:476:35)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:218:48)\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:526:32)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, {"timestamp": "2025-06-05T18:39:42.529Z", "elapsed": 7361, "level": "ERROR", "message": "Failed to fetch from Hugging Face API", "data": {"error": "HTTP error! status: 401", "stack": "Error: HTTP error! status: 401\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:476:35)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:218:48)\n    at fetchHuggingFacePapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:526:32)\n    at async fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:390:34)"}], "warnings": [{"timestamp": "2025-06-05T18:39:35.831Z", "elapsed": 663, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:39:42.565Z", "elapsed": 7397, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 7, "failedTests": 1, "totalErrors": 0, "totalLogs": 208, "screenshots": 3}}