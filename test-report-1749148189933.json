{"timestamp": "2025-06-05T18:29:33.261Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:29:40.432Z"}, {"name": "Loading Element Visible", "passed": false, "timestamp": "2025-06-05T18:29:41.676Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:29:41.809Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:29:41.815Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:29:43.228Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:29:43.938Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:29:46.740Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:29:47.383Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:29:37.609Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:29:37.900Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:29:38.093Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:29:38.098Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:29:38.099Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:29:38.099Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:29:38.099Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:29:38.099Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:29:38.100Z"}, {"type": "log", "text": "[INFO] Calling mockFetchPapers JSHandle@object", "timestamp": "2025-06-05T18:29:38.100Z"}, {"type": "log", "text": "[INFO] mockFetchPapers called - simulating API call JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:38.100Z"}, {"type": "warning", "text": "[WARN] Simulating network failure JSHandle@object", "timestamp": "2025-06-05T18:29:38.105Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:29:38.106Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:29:38.107Z"}, {"type": "error", "text": "[ERROR] Error fetching papers JSHandle@object", "timestamp": "2025-06-05T18:29:38.514Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:29:42.181Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:29:42.182Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:29:42.182Z"}, {"type": "log", "text": "[INFO] Calling mockFetchPapers JSHandle@object", "timestamp": "2025-06-05T18:29:42.182Z"}, {"type": "log", "text": "[INFO] mockFetchPapers called - simulating API call JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:42.182Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:29:43.332Z"}, {"type": "warning", "text": "[WARN] No word data available for color scheme change JSHandle@object", "timestamp": "2025-06-05T18:29:43.333Z"}, {"type": "log", "text": "[DEBUG] Mock API delay completed, returning papers JSHandle@object", "timestamp": "2025-06-05T18:29:43.699Z"}, {"type": "log", "text": "[INFO] Mock papers generated JSHandle@object", "timestamp": "2025-06-05T18:29:43.699Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:29:43.699Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:29:43.715Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:29:43.725Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:43.729Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:43.730Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:29:43.745Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:29:43.746Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:29:43.750Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:29:43.763Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:29:43.763Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:29:43.763Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:29:43.763Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:29:43.764Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:29:43.765Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:29:43.767Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:29:44.232Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:29:44.315Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:44.399Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:29:44.426Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.444Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.462Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.467Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.482Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.516Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.548Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.699Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.764Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:44.866Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.007Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.008Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.008Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.008Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.009Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.013Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.014Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.014Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.014Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.015Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.015Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.015Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.015Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.016Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.016Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.016Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.016Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.017Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.017Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.017Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.017Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.017Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.020Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.021Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.021Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.022Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.022Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.022Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.023Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.024Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.025Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.026Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.027Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.027Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.027Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.028Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.028Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.028Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.028Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.028Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:29:45.028Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:29:45.029Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:29:45.029Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:45.847Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:45.878Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:45.878Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:45.943Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:45.944Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:45.947Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:45.967Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:45.971Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:45.972Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:45.985Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:45.990Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:45.997Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.032Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.046Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.062Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.129Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.135Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.139Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.149Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.150Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.150Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.159Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.159Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.160Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.165Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.166Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.166Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.185Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.195Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.196Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:29:46.216Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:29:46.216Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:29:46.216Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749148180011.png", "timestamp": "2025-06-05T18:29:40.294Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749148181899.png", "timestamp": "2025-06-05T18:29:42.030Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749148187384.png", "timestamp": "2025-06-05T18:29:48.178Z"}], "errors": [], "performance": {"pageLoad": 3862}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:29:37.853Z", "elapsed": 2, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:29:37.853Z"}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.991Z", "elapsed": 140, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.992Z", "elapsed": 141, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.993Z", "elapsed": 142, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.993Z", "elapsed": 142, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.993Z", "elapsed": 142, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.994Z", "elapsed": 143, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.995Z", "elapsed": 144, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.995Z", "elapsed": 144, "level": "INFO", "message": "Calling mockFetchPapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.995Z", "elapsed": 144, "level": "INFO", "message": "mockFetchPapers called - simulating API call", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.996Z", "elapsed": 145, "level": "WARN", "message": "Simulating network failure", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.997Z", "elapsed": 146, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:37.997Z", "elapsed": 146, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:38.508Z", "elapsed": 657, "level": "ERROR", "message": "Error fetching papers", "data": {"error": "Simulated network failure", "stack": "Error: Simulated network failure\n    at file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:461:40", "timeMs": 514}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:210:48)\n    at fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:431:32)"}, {"timestamp": "2025-06-05T18:29:42.178Z", "elapsed": 4327, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:42.179Z", "elapsed": 4328, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:42.179Z", "elapsed": 4328, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:42.179Z", "elapsed": 4328, "level": "INFO", "message": "Calling mockFetchPapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:42.180Z", "elapsed": 4329, "level": "INFO", "message": "mockFetchPapers called - simulating API call", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.326Z", "elapsed": 5475, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.328Z", "elapsed": 5477, "level": "WARN", "message": "No word data available for color scheme change", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.691Z", "elapsed": 5840, "level": "DEBUG", "message": "Mock API delay completed, returning papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.692Z", "elapsed": 5841, "level": "INFO", "message": "Mock papers generated", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.693Z", "elapsed": 5842, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 1514}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.693Z", "elapsed": 5842, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.694Z", "elapsed": 5843, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.695Z", "elapsed": 5844, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 4041}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.696Z", "elapsed": 5845, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 493}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.698Z", "elapsed": 5847, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 204, "topWords": 50, "mostFrequent": "learning"}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.699Z", "elapsed": 5848, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 5}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.699Z", "elapsed": 5848, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.699Z", "elapsed": 5848, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.702Z", "elapsed": 5852, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.704Z", "elapsed": 5853, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.706Z", "elapsed": 5855, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.711Z", "elapsed": 5860, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.711Z", "elapsed": 5860, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 12}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.712Z", "elapsed": 5861, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 1533}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.881Z", "elapsed": 6030, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.882Z", "elapsed": 6031, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.882Z", "elapsed": 6031, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "bright", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.882Z", "elapsed": 6031, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.196Z", "elapsed": 6345, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 611, "y": 357}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.199Z", "elapsed": 6348, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 749, "y": 293}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.200Z", "elapsed": 6349, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 766, "y": 355}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.205Z", "elapsed": 6354, "level": "DEBUG", "message": "Word positioned", "data": {"text": "vision", "x": 491, "y": 230}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.206Z", "elapsed": 6355, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 732, "y": 179}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.207Z", "elapsed": 6356, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 431, "y": 213}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.210Z", "elapsed": 6359, "level": "DEBUG", "message": "Word positioned", "data": {"text": "quantum", "x": 659, "y": 281}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.211Z", "elapsed": 6360, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fine-tuning", "x": 607, "y": 262}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.211Z", "elapsed": 6360, "level": "DEBUG", "message": "Word positioned", "data": {"text": "speech", "x": 764, "y": 380}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.212Z", "elapsed": 6361, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 457, "y": 249}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.212Z", "elapsed": 6361, "level": "DEBUG", "message": "Word positioned", "data": {"text": "new", "x": 364, "y": 198}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.213Z", "elapsed": 6362, "level": "DEBUG", "message": "Word positioned", "data": {"text": "computer", "x": 414, "y": 322}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.213Z", "elapsed": 6362, "level": "DEBUG", "message": "Word positioned", "data": {"text": "privacy", "x": 692, "y": 243}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.214Z", "elapsed": 6363, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 568, "y": 268}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.214Z", "elapsed": 6363, "level": "DEBUG", "message": "Word positioned", "data": {"text": "adversarial", "x": 573, "y": 383}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.215Z", "elapsed": 6364, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 507, "y": 342}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.215Z", "elapsed": 6364, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 844, "y": 220}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.216Z", "elapsed": 6365, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comprehensive", "x": 836, "y": 317}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.216Z", "elapsed": 6365, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 869, "y": 298}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.217Z", "elapsed": 6366, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 709, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.217Z", "elapsed": 6366, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 533, "y": 237}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.221Z", "elapsed": 6370, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduces", "x": 449, "y": 159}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.222Z", "elapsed": 6371, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 811, "y": 245}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.223Z", "elapsed": 6372, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 645, "y": 218}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.224Z", "elapsed": 6373, "level": "DEBUG", "message": "Word positioned", "data": {"text": "contrastive", "x": 500, "y": 292}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.225Z", "elapsed": 6374, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 671, "y": 390}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.225Z", "elapsed": 6374, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 737, "y": 244}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.226Z", "elapsed": 6375, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 293, "y": 286}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.227Z", "elapsed": 6376, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 811, "y": 387}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.228Z", "elapsed": 6377, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 454, "y": 381}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.229Z", "elapsed": 6378, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 896, "y": 283}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.230Z", "elapsed": 6379, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 391, "y": 272}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.230Z", "elapsed": 6379, "level": "DEBUG", "message": "Word positioned", "data": {"text": "edge", "x": 809, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.231Z", "elapsed": 6380, "level": "DEBUG", "message": "Word positioned", "data": {"text": "approach", "x": 926, "y": 289}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.231Z", "elapsed": 6380, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 646, "y": 146}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.232Z", "elapsed": 6381, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mechanisms", "x": 495, "y": 144}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.232Z", "elapsed": 6381, "level": "DEBUG", "message": "Word positioned", "data": {"text": "survey", "x": 692, "y": 152}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.232Z", "elapsed": 6381, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 766, "y": 214}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.233Z", "elapsed": 6382, "level": "DEBUG", "message": "Word positioned", "data": {"text": "federated", "x": 611, "y": 184}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.233Z", "elapsed": 6382, "level": "DEBUG", "message": "Word positioned", "data": {"text": "differential", "x": 879, "y": 210}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.234Z", "elapsed": 6383, "level": "DEBUG", "message": "Word positioned", "data": {"text": "graph", "x": 443, "y": 270}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.237Z", "elapsed": 6386, "level": "DEBUG", "message": "Word positioned", "data": {"text": "molecular", "x": 508, "y": 412}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.238Z", "elapsed": 6387, "level": "DEBUG", "message": "Word positioned", "data": {"text": "reinforcement", "x": 334, "y": 316}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.239Z", "elapsed": 6388, "level": "DEBUG", "message": "Word positioned", "data": {"text": "autonomous", "x": 758, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.240Z", "elapsed": 6389, "level": "DEBUG", "message": "Word positioned", "data": {"text": "meta-learning", "x": 587, "y": 139}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.241Z", "elapsed": 6390, "level": "DEBUG", "message": "Word positioned", "data": {"text": "classification", "x": 576, "y": 189}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.242Z", "elapsed": 6391, "level": "DEBUG", "message": "Word positioned", "data": {"text": "algorithms", "x": 332, "y": 198}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.243Z", "elapsed": 6392, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multimodal", "x": 532, "y": 183}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.243Z", "elapsed": 6392, "level": "DEBUG", "message": "Word positioned", "data": {"text": "robustness", "x": 430, "y": 134}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.244Z", "elapsed": 6393, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deep", "x": 956, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.245Z", "elapsed": 6394, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:29:44.250Z", "elapsed": 6399, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.749Z", "elapsed": 7898, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.749Z", "elapsed": 7898, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.750Z", "elapsed": 7900, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 15, "visibleCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.922Z", "elapsed": 8071, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.923Z", "elapsed": 8072, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.924Z", "elapsed": 8073, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 15, "visibleCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.964Z", "elapsed": 8113, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.965Z", "elapsed": 8114, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.965Z", "elapsed": 8114, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 15, "visibleCount": 5}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.979Z", "elapsed": 8128, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.979Z", "elapsed": 8128, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:29:45.980Z", "elapsed": 8129, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.015Z", "elapsed": 8164, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.015Z", "elapsed": 8164, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.016Z", "elapsed": 8165, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.103Z", "elapsed": 8252, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.104Z", "elapsed": 8253, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.105Z", "elapsed": 8254, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.146Z", "elapsed": 8295, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.147Z", "elapsed": 8296, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.148Z", "elapsed": 8297, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.158Z", "elapsed": 8307, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.159Z", "elapsed": 8308, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.159Z", "elapsed": 8308, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.165Z", "elapsed": 8314, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.165Z", "elapsed": 8314, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.166Z", "elapsed": 8315, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.177Z", "elapsed": 8326, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.178Z", "elapsed": 8327, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.179Z", "elapsed": 8328, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.210Z", "elapsed": 8359, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.210Z", "elapsed": 8359, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:29:46.211Z", "elapsed": 8360, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 15, "visibleCount": 2}, "stack": null}], "stats": {"totalLogs": 127, "byLevel": {"DEBUG": 81, "INFO": 42, "WARN": 3, "ERROR": 1}, "errors": [{"timestamp": "2025-06-05T18:29:38.508Z", "elapsed": 657, "level": "ERROR", "message": "Error fetching papers", "data": {"error": "Simulated network failure", "stack": "Error: Simulated network failure\n    at file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:461:40", "timeMs": 514}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:210:48)\n    at fetchPapers (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:431:32)"}], "warnings": [{"timestamp": "2025-06-05T18:29:37.996Z", "elapsed": 145, "level": "WARN", "message": "Simulating network failure", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.328Z", "elapsed": 5477, "level": "WARN", "message": "No word data available for color scheme change", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:29:43.704Z", "elapsed": 5853, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 7, "failedTests": 1, "totalErrors": 0, "totalLogs": 128, "screenshots": 3}}