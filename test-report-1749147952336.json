{"timestamp": "2025-06-05T18:25:43.423Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:25:46.730Z"}, {"name": "Loading Element Visible", "passed": true, "timestamp": "2025-06-05T18:25:46.813Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:25:46.937Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:25:46.966Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:25:50.124Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:25:50.807Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:25:52.029Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:25:52.047Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:25:45.190Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:25:45.508Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:25:45.695Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:25:45.697Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:25:45.698Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:25:45.703Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:25:45.709Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:25:45.723Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:25:45.723Z"}, {"type": "log", "text": "[INFO] Calling mockFetchPapers JSHandle@object", "timestamp": "2025-06-05T18:25:45.724Z"}, {"type": "log", "text": "[INFO] mockFetchPapers called - simulating API call JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:45.724Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:25:45.724Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:25:45.724Z"}, {"type": "log", "text": "[DEBUG] Mock API delay completed, returning papers JSHandle@object", "timestamp": "2025-06-05T18:25:47.272Z"}, {"type": "log", "text": "[INFO] Mock papers generated JSHandle@object", "timestamp": "2025-06-05T18:25:47.294Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:25:47.313Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:47.314Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:47.552Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:47.655Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:47.698Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:25:47.763Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:25:47.793Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:25:47.857Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:47.895Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:47.945Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:25:47.995Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:25:48.013Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:25:48.060Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:25:48.064Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:25:48.065Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:48.186Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:48.215Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:48.255Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:25:48.263Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.382Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.398Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.409Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.410Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.413Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.414Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.446Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.461Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.476Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.497Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.498Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.498Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.508Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.509Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.511Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.512Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.525Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.527Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.528Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.528Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.528Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.529Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.529Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.530Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.530Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.530Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.530Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.543Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.543Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.545Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.546Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.546Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.546Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.546Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.547Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.547Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.547Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.547Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.573Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.578Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.579Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.579Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.579Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.579Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.580Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.580Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.580Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.589Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.590Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:48.590Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:48.590Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:25:48.591Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:25:49.064Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:25:49.064Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:25:49.064Z"}, {"type": "log", "text": "[INFO] Calling mockFetchPapers JSHandle@object", "timestamp": "2025-06-05T18:25:49.065Z"}, {"type": "log", "text": "[INFO] mockFetchPapers called - simulating API call JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:49.065Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:25:50.293Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:50.293Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:50.293Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:25:50.294Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:50.398Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:50.399Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:25:50.405Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:50.560Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:50.560Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:25:50.560Z"}, {"type": "log", "text": "[DEBUG] Mock API delay completed, returning papers JSHandle@object", "timestamp": "2025-06-05T18:25:50.664Z"}, {"type": "log", "text": "[INFO] Mock papers generated JSHandle@object", "timestamp": "2025-06-05T18:25:50.665Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:25:50.666Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:50.670Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:50.673Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:50.674Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:50.674Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:25:50.674Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:25:50.674Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:25:50.675Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:25:50.676Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:50.677Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:50.677Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:50.677Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:25:50.677Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:50.938Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:50.948Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:50.994Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.019Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.026Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.028Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.029Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.029Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.030Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.030Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.030Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.031Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.031Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.031Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.031Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.035Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.037Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.042Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.043Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.044Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.045Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.045Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.045Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.046Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.046Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.047Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.047Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.047Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.047Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.048Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.048Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.053Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.056Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.056Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.057Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.058Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.059Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.059Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.060Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.060Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.061Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.062Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.062Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.062Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.063Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:25:51.064Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.065Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.065Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.065Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.065Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.070Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.072Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.073Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.073Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.074Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.074Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.074Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.074Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.075Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.076Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.077Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.078Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.079Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.080Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.096Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.097Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.098Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.098Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.104Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.105Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.106Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.106Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.107Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.107Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.107Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.107Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.108Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:25:51.109Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:25:51.109Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:25:51.109Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.192Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.193Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.193Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.214Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.215Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.215Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.277Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.278Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.278Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.307Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.309Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.310Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.392Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.392Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.394Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.413Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.414Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.414Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.439Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.439Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.439Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.447Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.448Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.448Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.476Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.478Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.478Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.495Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.496Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.496Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:25:51.516Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:25:51.521Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:25:51.522Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749147946508.png", "timestamp": "2025-06-05T18:25:46.722Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749147948593.png", "timestamp": "2025-06-05T18:25:48.988Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749147952047.png", "timestamp": "2025-06-05T18:25:52.323Z"}], "errors": [], "performance": {"pageLoad": 2042}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:25:45.504Z", "elapsed": 1, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:25:45.504Z"}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.643Z", "elapsed": 140, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.644Z", "elapsed": 141, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.644Z", "elapsed": 141, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.644Z", "elapsed": 141, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.645Z", "elapsed": 142, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.645Z", "elapsed": 142, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.645Z", "elapsed": 143, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.646Z", "elapsed": 143, "level": "INFO", "message": "Calling mockFetchPapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.646Z", "elapsed": 143, "level": "INFO", "message": "mockFetchPapers called - simulating API call", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.647Z", "elapsed": 144, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:45.647Z", "elapsed": 144, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.149Z", "elapsed": 1646, "level": "DEBUG", "message": "Mock API delay completed, returning papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.150Z", "elapsed": 1647, "level": "INFO", "message": "Mock papers generated", "data": {"paperCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.152Z", "elapsed": 1649, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 6, "fetchTimeMs": 1507}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.153Z", "elapsed": 1650, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.154Z", "elapsed": 1651, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.155Z", "elapsed": 1652, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 1657}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.156Z", "elapsed": 1653, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 204}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.159Z", "elapsed": 1656, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 103, "topWords": 50, "mostFrequent": "models"}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.160Z", "elapsed": 1657, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.160Z", "elapsed": 1657, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.161Z", "elapsed": 1658, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.162Z", "elapsed": 1659, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.162Z", "elapsed": 1659, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.163Z", "elapsed": 1660, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.167Z", "elapsed": 1664, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.168Z", "elapsed": 1665, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 8}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.169Z", "elapsed": 1666, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 1524}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.304Z", "elapsed": 1801, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.304Z", "elapsed": 1801, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.304Z", "elapsed": 1801, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.305Z", "elapsed": 1802, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.470Z", "elapsed": 1967, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 865, "y": 331}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.473Z", "elapsed": 1970, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 788, "y": 353}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.474Z", "elapsed": 1971, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 725, "y": 275}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.474Z", "elapsed": 1972, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fine-tuning", "x": 680, "y": 193}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.475Z", "elapsed": 1972, "level": "DEBUG", "message": "Word positioned", "data": {"text": "speech", "x": 635, "y": 363}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.475Z", "elapsed": 1972, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 589, "y": 302}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.476Z", "elapsed": 1973, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 746, "y": 375}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.477Z", "elapsed": 1974, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 526, "y": 145}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.478Z", "elapsed": 1975, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 559, "y": 331}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.478Z", "elapsed": 1975, "level": "DEBUG", "message": "Word positioned", "data": {"text": "contrastive", "x": 585, "y": 363}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.478Z", "elapsed": 1975, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 463, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.479Z", "elapsed": 1976, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 664, "y": 254}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.479Z", "elapsed": 1976, "level": "DEBUG", "message": "Word positioned", "data": {"text": "edge", "x": 815, "y": 142}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.480Z", "elapsed": 1977, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multilingual", "x": 518, "y": 228}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.481Z", "elapsed": 1978, "level": "DEBUG", "message": "Word positioned", "data": {"text": "present", "x": 482, "y": 250}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.481Z", "elapsed": 1978, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architectures", "x": 868, "y": 266}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.484Z", "elapsed": 1981, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 439, "y": 193}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.484Z", "elapsed": 1981, "level": "DEBUG", "message": "Word positioned", "data": {"text": "low-resource", "x": 625, "y": 177}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.485Z", "elapsed": 1982, "level": "DEBUG", "message": "Word positioned", "data": {"text": "parameter-efficient", "x": 737, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.485Z", "elapsed": 1982, "level": "DEBUG", "message": "Word positioned", "data": {"text": "method", "x": 520, "y": 319}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.486Z", "elapsed": 1983, "level": "DEBUG", "message": "Word positioned", "data": {"text": "achieves", "x": 581, "y": 247}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.486Z", "elapsed": 1983, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 408, "y": 359}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.487Z", "elapsed": 1984, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficiency", "x": 840, "y": 272}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.487Z", "elapsed": 1984, "level": "DEBUG", "message": "Word positioned", "data": {"text": "self-supervised", "x": 345, "y": 224}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.487Z", "elapsed": 1984, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 506, "y": 121}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.488Z", "elapsed": 1985, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 379, "y": 203}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.489Z", "elapsed": 1986, "level": "DEBUG", "message": "Word positioned", "data": {"text": "image", "x": 524, "y": 350}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.489Z", "elapsed": 1986, "level": "DEBUG", "message": "Word positioned", "data": {"text": "ethical", "x": 428, "y": 262}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.491Z", "elapsed": 1988, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deployment", "x": 581, "y": 127}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.491Z", "elapsed": 1988, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bias", "x": 332, "y": 194}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.492Z", "elapsed": 1989, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 630, "y": 128}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.492Z", "elapsed": 1989, "level": "DEBUG", "message": "Word positioned", "data": {"text": "search", "x": 809, "y": 242}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.493Z", "elapsed": 1990, "level": "DEBUG", "message": "Word positioned", "data": {"text": "devices", "x": 642, "y": 229}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.493Z", "elapsed": 1990, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nas", "x": 558, "y": 384}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.493Z", "elapsed": 1990, "level": "DEBUG", "message": "Word positioned", "data": {"text": "100", "x": 534, "y": 166}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.494Z", "elapsed": 1991, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comprehensive", "x": 879, "y": 151}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.494Z", "elapsed": 1991, "level": "DEBUG", "message": "Word positioned", "data": {"text": "study", "x": 740, "y": 340}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.495Z", "elapsed": 1992, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applied", "x": 619, "y": 310}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.495Z", "elapsed": 1992, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 789, "y": 158}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.496Z", "elapsed": 1993, "level": "DEBUG", "message": "Word positioned", "data": {"text": "languages", "x": 799, "y": 274}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.496Z", "elapsed": 1993, "level": "DEBUG", "message": "Word positioned", "data": {"text": "show", "x": 847, "y": 217}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.496Z", "elapsed": 1993, "level": "DEBUG", "message": "Word positioned", "data": {"text": "significant", "x": 410, "y": 328}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.497Z", "elapsed": 1994, "level": "DEBUG", "message": "Word positioned", "data": {"text": "improvements", "x": 720, "y": 194}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.497Z", "elapsed": 1994, "level": "DEBUG", "message": "Word positioned", "data": {"text": "pairs", "x": 758, "y": 296}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.498Z", "elapsed": 1995, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nlp", "x": 649, "y": 296}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.500Z", "elapsed": 1997, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficient", "x": 486, "y": 378}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.501Z", "elapsed": 1998, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 667, "y": 354}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.502Z", "elapsed": 1999, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduces", "x": 282, "y": 238}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.503Z", "elapsed": 2000, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 343, "y": 306}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.503Z", "elapsed": 2000, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comparable", "x": 593, "y": 209}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.504Z", "elapsed": 2001, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:25:47.507Z", "elapsed": 2004, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:49.056Z", "elapsed": 3553, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:49.057Z", "elapsed": 3554, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:49.057Z", "elapsed": 3554, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:49.057Z", "elapsed": 3554, "level": "INFO", "message": "Calling mockFetchPapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:49.058Z", "elapsed": 3555, "level": "INFO", "message": "mockFetchPapers called - simulating API call", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.289Z", "elapsed": 4786, "level": "INFO", "message": "Color scheme changed", "data": {"newScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.290Z", "elapsed": 4787, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.291Z", "elapsed": 4788, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.291Z", "elapsed": 4788, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.394Z", "elapsed": 4891, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.395Z", "elapsed": 4892, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.396Z", "elapsed": 4893, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.501Z", "elapsed": 4998, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.502Z", "elapsed": 4999, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.503Z", "elapsed": 5000, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.567Z", "elapsed": 5064, "level": "DEBUG", "message": "Mock API delay completed, returning papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.569Z", "elapsed": 5066, "level": "INFO", "message": "Mock papers generated", "data": {"paperCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.569Z", "elapsed": 5066, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 6, "fetchTimeMs": 1513}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.570Z", "elapsed": 5067, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.571Z", "elapsed": 5068, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.571Z", "elapsed": 5068, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 1657}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.571Z", "elapsed": 5068, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 204}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.572Z", "elapsed": 5069, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 103, "topWords": 50, "mostFrequent": "models"}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.572Z", "elapsed": 5069, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 2}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.573Z", "elapsed": 5070, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.573Z", "elapsed": 5070, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.573Z", "elapsed": 5070, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.573Z", "elapsed": 5070, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.574Z", "elapsed": 5071, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.575Z", "elapsed": 5072, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.575Z", "elapsed": 5072, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 2}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.575Z", "elapsed": 5072, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 1519}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.605Z", "elapsed": 5102, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.606Z", "elapsed": 5103, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.607Z", "elapsed": 5104, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "bright", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.607Z", "elapsed": 5104, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.689Z", "elapsed": 5186, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 825, "y": 129}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.691Z", "elapsed": 5188, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 747, "y": 100}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.691Z", "elapsed": 5188, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 837, "y": 174}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.692Z", "elapsed": 5189, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fine-tuning", "x": 676, "y": 212}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.693Z", "elapsed": 5190, "level": "DEBUG", "message": "Word positioned", "data": {"text": "speech", "x": 601, "y": 256}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.693Z", "elapsed": 5190, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 655, "y": 320}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.694Z", "elapsed": 5191, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 738, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.694Z", "elapsed": 5191, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 739, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.695Z", "elapsed": 5192, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 557, "y": 324}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.695Z", "elapsed": 5192, "level": "DEBUG", "message": "Word positioned", "data": {"text": "contrastive", "x": 584, "y": 328}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.696Z", "elapsed": 5193, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 526, "y": 174}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.696Z", "elapsed": 5193, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 803, "y": 281}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.697Z", "elapsed": 5194, "level": "DEBUG", "message": "Word positioned", "data": {"text": "edge", "x": 495, "y": 173}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.697Z", "elapsed": 5194, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multilingual", "x": 482, "y": 233}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.698Z", "elapsed": 5195, "level": "DEBUG", "message": "Word positioned", "data": {"text": "present", "x": 563, "y": 287}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.699Z", "elapsed": 5196, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architectures", "x": 914, "y": 142}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.699Z", "elapsed": 5196, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 695, "y": 344}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.700Z", "elapsed": 5197, "level": "DEBUG", "message": "Word positioned", "data": {"text": "low-resource", "x": 667, "y": 266}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.701Z", "elapsed": 5198, "level": "DEBUG", "message": "Word positioned", "data": {"text": "parameter-efficient", "x": 446, "y": 205}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.701Z", "elapsed": 5198, "level": "DEBUG", "message": "Word positioned", "data": {"text": "method", "x": 776, "y": 157}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.702Z", "elapsed": 5199, "level": "DEBUG", "message": "Word positioned", "data": {"text": "achieves", "x": 561, "y": 220}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.702Z", "elapsed": 5199, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 367, "y": 165}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.703Z", "elapsed": 5200, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficiency", "x": 614, "y": 179}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.704Z", "elapsed": 5201, "level": "DEBUG", "message": "Word positioned", "data": {"text": "self-supervised", "x": 706, "y": 236}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.705Z", "elapsed": 5202, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 421, "y": 342}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.706Z", "elapsed": 5203, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 320, "y": 373}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.706Z", "elapsed": 5203, "level": "DEBUG", "message": "Word positioned", "data": {"text": "image", "x": 659, "y": 138}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.707Z", "elapsed": 5204, "level": "DEBUG", "message": "Word positioned", "data": {"text": "ethical", "x": 335, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.707Z", "elapsed": 5204, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deployment", "x": 414, "y": 252}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.707Z", "elapsed": 5204, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bias", "x": 772, "y": 238}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.708Z", "elapsed": 5205, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 875, "y": 314}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.708Z", "elapsed": 5205, "level": "DEBUG", "message": "Word positioned", "data": {"text": "search", "x": 344, "y": 189}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.708Z", "elapsed": 5205, "level": "DEBUG", "message": "Word positioned", "data": {"text": "devices", "x": 717, "y": 290}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.709Z", "elapsed": 5206, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nas", "x": 551, "y": 246}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.709Z", "elapsed": 5206, "level": "DEBUG", "message": "Word positioned", "data": {"text": "100", "x": 391, "y": 349}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.710Z", "elapsed": 5207, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comprehensive", "x": 365, "y": 254}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.710Z", "elapsed": 5207, "level": "DEBUG", "message": "Word positioned", "data": {"text": "study", "x": 457, "y": 302}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.710Z", "elapsed": 5208, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applied", "x": 811, "y": 355}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.711Z", "elapsed": 5208, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 598, "y": 152}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.711Z", "elapsed": 5208, "level": "DEBUG", "message": "Word positioned", "data": {"text": "languages", "x": 454, "y": 164}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.711Z", "elapsed": 5208, "level": "DEBUG", "message": "Word positioned", "data": {"text": "show", "x": 452, "y": 354}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.712Z", "elapsed": 5209, "level": "DEBUG", "message": "Word positioned", "data": {"text": "significant", "x": 335, "y": 136}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.712Z", "elapsed": 5209, "level": "DEBUG", "message": "Word positioned", "data": {"text": "improvements", "x": 465, "y": 138}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.713Z", "elapsed": 5210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "pairs", "x": 806, "y": 250}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.713Z", "elapsed": 5210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nlp", "x": 340, "y": 222}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.713Z", "elapsed": 5210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficient", "x": 357, "y": 393}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.714Z", "elapsed": 5211, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 498, "y": 264}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.714Z", "elapsed": 5211, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduces", "x": 318, "y": 251}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.715Z", "elapsed": 5212, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 513, "y": 298}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.717Z", "elapsed": 5214, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comparable", "x": 754, "y": 347}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.719Z", "elapsed": 5216, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.721Z", "elapsed": 5218, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.734Z", "elapsed": 5231, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.736Z", "elapsed": 5233, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.737Z", "elapsed": 5234, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "bright", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.737Z", "elapsed": 5234, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.819Z", "elapsed": 5316, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 767, "y": 215}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.821Z", "elapsed": 5318, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 677, "y": 259}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.823Z", "elapsed": 5320, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 585, "y": 296}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.824Z", "elapsed": 5321, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fine-tuning", "x": 733, "y": 277}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.825Z", "elapsed": 5322, "level": "DEBUG", "message": "Word positioned", "data": {"text": "speech", "x": 598, "y": 349}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.826Z", "elapsed": 5323, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 491, "y": 336}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.827Z", "elapsed": 5324, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 453, "y": 188}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.828Z", "elapsed": 5325, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 662, "y": 312}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.828Z", "elapsed": 5325, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 817, "y": 369}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.828Z", "elapsed": 5325, "level": "DEBUG", "message": "Word positioned", "data": {"text": "contrastive", "x": 705, "y": 173}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.829Z", "elapsed": 5326, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 622, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.829Z", "elapsed": 5326, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 407, "y": 347}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.830Z", "elapsed": 5327, "level": "DEBUG", "message": "Word positioned", "data": {"text": "edge", "x": 465, "y": 313}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.830Z", "elapsed": 5327, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multilingual", "x": 787, "y": 365}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.834Z", "elapsed": 5331, "level": "DEBUG", "message": "Word positioned", "data": {"text": "present", "x": 680, "y": 189}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.836Z", "elapsed": 5333, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architectures", "x": 821, "y": 165}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.837Z", "elapsed": 5334, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 842, "y": 300}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.837Z", "elapsed": 5334, "level": "DEBUG", "message": "Word positioned", "data": {"text": "low-resource", "x": 432, "y": 266}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.838Z", "elapsed": 5335, "level": "DEBUG", "message": "Word positioned", "data": {"text": "parameter-efficient", "x": 835, "y": 275}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.839Z", "elapsed": 5336, "level": "DEBUG", "message": "Word positioned", "data": {"text": "method", "x": 787, "y": 241}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.839Z", "elapsed": 5336, "level": "DEBUG", "message": "Word positioned", "data": {"text": "achieves", "x": 863, "y": 236}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.864Z", "elapsed": 5361, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 694, "y": 327}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.865Z", "elapsed": 5362, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficiency", "x": 354, "y": 265}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.866Z", "elapsed": 5363, "level": "DEBUG", "message": "Word positioned", "data": {"text": "self-supervised", "x": 516, "y": 134}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.867Z", "elapsed": 5364, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 562, "y": 268}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.867Z", "elapsed": 5364, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 691, "y": 132}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.868Z", "elapsed": 5365, "level": "DEBUG", "message": "Word positioned", "data": {"text": "image", "x": 562, "y": 366}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.869Z", "elapsed": 5366, "level": "DEBUG", "message": "Word positioned", "data": {"text": "ethical", "x": 692, "y": 354}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.869Z", "elapsed": 5366, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deployment", "x": 389, "y": 225}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.870Z", "elapsed": 5367, "level": "DEBUG", "message": "Word positioned", "data": {"text": "bias", "x": 867, "y": 159}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.870Z", "elapsed": 5367, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 611, "y": 218}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.871Z", "elapsed": 5368, "level": "DEBUG", "message": "Word positioned", "data": {"text": "search", "x": 694, "y": 291}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.871Z", "elapsed": 5368, "level": "DEBUG", "message": "Word positioned", "data": {"text": "devices", "x": 307, "y": 173}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.872Z", "elapsed": 5369, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nas", "x": 671, "y": 153}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.872Z", "elapsed": 5369, "level": "DEBUG", "message": "Word positioned", "data": {"text": "100", "x": 297, "y": 317}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.872Z", "elapsed": 5369, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comprehensive", "x": 331, "y": 326}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.873Z", "elapsed": 5370, "level": "DEBUG", "message": "Word positioned", "data": {"text": "study", "x": 550, "y": 241}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.873Z", "elapsed": 5370, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applied", "x": 864, "y": 132}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.874Z", "elapsed": 5371, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 907, "y": 255}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.877Z", "elapsed": 5374, "level": "DEBUG", "message": "Word positioned", "data": {"text": "languages", "x": 487, "y": 245}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.878Z", "elapsed": 5375, "level": "DEBUG", "message": "Word positioned", "data": {"text": "show", "x": 449, "y": 212}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.879Z", "elapsed": 5376, "level": "DEBUG", "message": "Word positioned", "data": {"text": "significant", "x": 394, "y": 179}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.880Z", "elapsed": 5377, "level": "DEBUG", "message": "Word positioned", "data": {"text": "improvements", "x": 336, "y": 137}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.884Z", "elapsed": 5381, "level": "DEBUG", "message": "Word positioned", "data": {"text": "pairs", "x": 680, "y": 227}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.885Z", "elapsed": 5382, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nlp", "x": 313, "y": 243}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.886Z", "elapsed": 5383, "level": "DEBUG", "message": "Word positioned", "data": {"text": "efficient", "x": 636, "y": 320}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.887Z", "elapsed": 5384, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 791, "y": 122}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.888Z", "elapsed": 5385, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduces", "x": 925, "y": 229}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.889Z", "elapsed": 5386, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 581, "y": 210}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.890Z", "elapsed": 5387, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comparable", "x": 740, "y": 143}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.891Z", "elapsed": 5388, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.893Z", "elapsed": 5390, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.165Z", "elapsed": 5662, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.167Z", "elapsed": 5664, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.168Z", "elapsed": 5665, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 6, "visibleCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.209Z", "elapsed": 5706, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.210Z", "elapsed": 5707, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.210Z", "elapsed": 5707, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 6, "visibleCount": 5}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.271Z", "elapsed": 5768, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.271Z", "elapsed": 5768, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.272Z", "elapsed": 5769, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 6, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.288Z", "elapsed": 5785, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.289Z", "elapsed": 5786, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.289Z", "elapsed": 5786, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 6, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.347Z", "elapsed": 5844, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.347Z", "elapsed": 5844, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.348Z", "elapsed": 5845, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 6, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.411Z", "elapsed": 5908, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.412Z", "elapsed": 5909, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.413Z", "elapsed": 5910, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 6, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.429Z", "elapsed": 5926, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.429Z", "elapsed": 5926, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.430Z", "elapsed": 5927, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 6, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.443Z", "elapsed": 5940, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.444Z", "elapsed": 5941, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.445Z", "elapsed": 5942, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 6, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.466Z", "elapsed": 5963, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.467Z", "elapsed": 5964, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.468Z", "elapsed": 5965, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 6, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.491Z", "elapsed": 5988, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.492Z", "elapsed": 5989, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.493Z", "elapsed": 5990, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 6, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.511Z", "elapsed": 6008, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.511Z", "elapsed": 6008, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:25:51.512Z", "elapsed": 6009, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 6, "visibleCount": 1}, "stack": null}], "stats": {"totalLogs": 262, "byLevel": {"DEBUG": 194, "INFO": 63, "WARN": 5, "ERROR": 0}, "errors": [], "warnings": [{"timestamp": "2025-06-05T18:25:47.162Z", "elapsed": 1659, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.291Z", "elapsed": 4788, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.396Z", "elapsed": 4893, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.503Z", "elapsed": 5000, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:25:50.573Z", "elapsed": 5070, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 8, "failedTests": 0, "totalErrors": 0, "totalLogs": 263, "screenshots": 3}}