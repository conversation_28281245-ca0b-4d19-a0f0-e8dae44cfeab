{"timestamp": "2025-06-05T18:54:46.148Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:54:51.805Z"}, {"name": "Loading Element Visible", "passed": false, "timestamp": "2025-06-05T18:54:51.989Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:54:52.003Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:54:52.006Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:54:54.239Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:54:54.867Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:54:55.868Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:54:55.904Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:54:48.252Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:54:48.622Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:54:48.792Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:54:48.792Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:54:48.793Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:54:48.800Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:54:48.802Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:54:48.809Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:54:48.809Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:54:48.817Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:54:48.818Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:54:48.819Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:54:48.819Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:54:49.920Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:54:49.922Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:54:49.923Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:54:49.923Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:54:49.924Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:49.924Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:49.925Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:54:49.955Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:54:49.972Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:54:49.976Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:54:49.982Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:54:49.982Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:54:49.982Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:54:49.982Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:54:49.983Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:54:49.983Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:54:49.983Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:54:49.983Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:54:50.094Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:54:50.097Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:50.097Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:54:50.107Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.301Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.303Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.312Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.318Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.335Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.336Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.336Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.336Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.336Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.336Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.336Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.337Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.422Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.437Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.438Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.438Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.438Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.438Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.439Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.439Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.439Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.439Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.440Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.440Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.441Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.449Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.453Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.454Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.454Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.454Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:54:50.455Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:54:50.456Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:54:53.045Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:54:53.051Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:54:53.054Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:54:53.057Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:54:53.059Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:54:53.759Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:54:53.772Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:54:53.772Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:54:53.776Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:54:53.777Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:53.786Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:53.787Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:54:53.787Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:54:53.787Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:54:53.787Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:54:53.788Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:54:53.788Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:54:53.788Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:54:53.788Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:54:53.788Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:54:53.789Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:54:53.789Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:54:53.789Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:54:53.858Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:54:53.862Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:53.875Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:54:53.875Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.024Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.088Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.089Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.133Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.158Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.168Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.171Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.172Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.174Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.175Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.190Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.191Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.192Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.192Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.193Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.223Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.223Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.223Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.223Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.225Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.225Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.225Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.225Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.225Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.226Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.226Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.233Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.233Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.233Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.234Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.234Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.234Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.234Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.235Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.235Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.235Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.235Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.236Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.236Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.236Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.236Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.237Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.237Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:54:54.237Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:54:54.237Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:54:54.239Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:54:54.354Z"}, {"type": "log", "text": "[DEBUG] Updating word cloud colors JSHandle@object", "timestamp": "2025-06-05T18:54:54.354Z"}, {"type": "log", "text": "[INFO] Word cloud colors updated JSHandle@object", "timestamp": "2025-06-05T18:54:54.355Z"}, {"type": "log", "text": "[DEBUG] Word hovered JSHandle@object", "timestamp": "2025-06-05T18:54:55.020Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.069Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.070Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.071Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.083Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.084Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.087Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.123Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.124Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.125Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.135Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.138Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.139Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.191Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.192Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.192Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.221Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.223Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.224Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.243Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.252Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.254Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.275Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.286Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.287Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.303Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.304Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.304Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.313Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.319Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.319Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:54:55.326Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:54:55.327Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:54:55.337Z"}, {"type": "log", "text": "[DEBUG] Word hovered JSHandle@object", "timestamp": "2025-06-05T18:54:55.354Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749149691074.png", "timestamp": "2025-06-05T18:54:51.791Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749149692059.png", "timestamp": "2025-06-05T18:54:52.792Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749149695906.png", "timestamp": "2025-06-05T18:54:56.187Z"}], "errors": [], "performance": {"pageLoad": 3451}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:54:48.608Z", "elapsed": 1, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:54:48.607Z"}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.765Z", "elapsed": 158, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.767Z", "elapsed": 160, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.767Z", "elapsed": 160, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.768Z", "elapsed": 161, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.768Z", "elapsed": 161, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.769Z", "elapsed": 162, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.769Z", "elapsed": 162, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.770Z", "elapsed": 163, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.770Z", "elapsed": 163, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.772Z", "elapsed": 165, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:48.772Z", "elapsed": 165, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.919Z", "elapsed": 1312, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 663008, "dataType": "object", "isArray": true, "searchQuery": "AI"}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.921Z", "elapsed": 1314, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 111, "processedCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.921Z", "elapsed": 1314, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 1152}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.921Z", "elapsed": 1314, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.922Z", "elapsed": 1315, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.922Z", "elapsed": 1316, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 21903}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.923Z", "elapsed": 1316, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3059}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.930Z", "elapsed": 1323, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1131, "topWords": 50, "mostFrequent": "language"}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.932Z", "elapsed": 1325, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 10}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.933Z", "elapsed": 1326, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 15, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.934Z", "elapsed": 1327, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.935Z", "elapsed": 1328, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.937Z", "elapsed": 1330, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.938Z", "elapsed": 1331, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.939Z", "elapsed": 1332, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.941Z", "elapsed": 1334, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.942Z", "elapsed": 1335, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 7}, "stack": null}, {"timestamp": "2025-06-05T18:54:49.942Z", "elapsed": 1335, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 1174}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.065Z", "elapsed": 1458, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.065Z", "elapsed": 1458, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.066Z", "elapsed": 1459, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.066Z", "elapsed": 1459, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.264Z", "elapsed": 1657, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 769, "y": 269}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.267Z", "elapsed": 1660, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 613, "y": 223}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.268Z", "elapsed": 1662, "level": "DEBUG", "message": "Word positioned", "data": {"text": "systems", "x": 618, "y": 310}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.269Z", "elapsed": 1662, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agents", "x": 471, "y": 328}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.270Z", "elapsed": 1663, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 442, "y": 383}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.271Z", "elapsed": 1664, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 553, "y": 351}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.272Z", "elapsed": 1665, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 779, "y": 136}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.273Z", "elapsed": 1666, "level": "DEBUG", "message": "Word positioned", "data": {"text": "research", "x": 873, "y": 257}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.274Z", "elapsed": 1667, "level": "DEBUG", "message": "Word positioned", "data": {"text": "system", "x": 365, "y": 317}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.275Z", "elapsed": 1668, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agent", "x": 451, "y": 245}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.276Z", "elapsed": 1669, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 353, "y": 145}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.279Z", "elapsed": 1672, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agentic", "x": 622, "y": 147}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.280Z", "elapsed": 1673, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 513, "y": 154}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.281Z", "elapsed": 1674, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llms", "x": 402, "y": 195}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.282Z", "elapsed": 1675, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 411, "y": 109}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.283Z", "elapsed": 1676, "level": "DEBUG", "message": "Word positioned", "data": {"text": "engineering", "x": 316, "y": 280}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.285Z", "elapsed": 1678, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 775, "y": 223}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.286Z", "elapsed": 1679, "level": "DEBUG", "message": "Word positioned", "data": {"text": "human", "x": 683, "y": 348}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.288Z", "elapsed": 1681, "level": "DEBUG", "message": "Word positioned", "data": {"text": "benchmarks", "x": 927, "y": 318}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.289Z", "elapsed": 1682, "level": "DEBUG", "message": "Word positioned", "data": {"text": "their", "x": 255, "y": 211}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.289Z", "elapsed": 1682, "level": "DEBUG", "message": "Word positioned", "data": {"text": "memory", "x": 721, "y": 374}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.290Z", "elapsed": 1683, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 183, "y": 164}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.291Z", "elapsed": 1684, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 196, "y": 259}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.292Z", "elapsed": 1685, "level": "DEBUG", "message": "Word positioned", "data": {"text": "suggestions", "x": 625, "y": 98}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.292Z", "elapsed": 1685, "level": "DEBUG", "message": "Word positioned", "data": {"text": "code", "x": 766, "y": 385}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.292Z", "elapsed": 1685, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 576, "y": 271}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.293Z", "elapsed": 1686, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scientific", "x": 865, "y": 98}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.296Z", "elapsed": 1689, "level": "DEBUG", "message": "Word positioned", "data": {"text": "second", "x": 913, "y": 140}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.296Z", "elapsed": 1689, "level": "DEBUG", "message": "Word positioned", "data": {"text": "through", "x": 258, "y": 335}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.297Z", "elapsed": 1690, "level": "DEBUG", "message": "Word positioned", "data": {"text": "responses", "x": 516, "y": 59}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.298Z", "elapsed": 1691, "level": "DEBUG", "message": "Word positioned", "data": {"text": "evaluation", "x": 710, "y": 54}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.298Z", "elapsed": 1692, "level": "DEBUG", "message": "Word positioned", "data": {"text": "capabilities", "x": 813, "y": 367}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.299Z", "elapsed": 1692, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 467, "y": 413}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.299Z", "elapsed": 1692, "level": "DEBUG", "message": "Word positioned", "data": {"text": "github", "x": 757, "y": 176}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.300Z", "elapsed": 1693, "level": "DEBUG", "message": "Word positioned", "data": {"text": "music", "x": 629, "y": 421}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.300Z", "elapsed": 1693, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tools", "x": 209, "y": 298}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.301Z", "elapsed": 1694, "level": "DEBUG", "message": "Word positioned", "data": {"text": "solutions", "x": 353, "y": 62}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.302Z", "elapsed": 1695, "level": "DEBUG", "message": "Word positioned", "data": {"text": "user", "x": 483, "y": 148}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.302Z", "elapsed": 1695, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduce", "x": 1018, "y": 185}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.303Z", "elapsed": 1696, "level": "DEBUG", "message": "Word positioned", "data": {"text": "first", "x": 948, "y": 120}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.304Z", "elapsed": 1697, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 525, "y": 241}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.304Z", "elapsed": 1697, "level": "DEBUG", "message": "Word positioned", "data": {"text": "interaction", "x": 974, "y": 274}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.305Z", "elapsed": 1698, "level": "DEBUG", "message": "Word positioned", "data": {"text": "provide", "x": 1015, "y": 265}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.305Z", "elapsed": 1698, "level": "DEBUG", "message": "Word positioned", "data": {"text": "software", "x": 517, "y": 450}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.306Z", "elapsed": 1699, "level": "DEBUG", "message": "Word positioned", "data": {"text": "questions", "x": 240, "y": 92}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.306Z", "elapsed": 1699, "level": "DEBUG", "message": "Word positioned", "data": {"text": "users", "x": 860, "y": 380}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.307Z", "elapsed": 1700, "level": "DEBUG", "message": "Word positioned", "data": {"text": "knowledge", "x": 1071, "y": 283}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.308Z", "elapsed": 1701, "level": "DEBUG", "message": "Word positioned", "data": {"text": "been", "x": 490, "y": 248}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.309Z", "elapsed": 1702, "level": "DEBUG", "message": "Word positioned", "data": {"text": "task", "x": 355, "y": 429}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.309Z", "elapsed": 1702, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 780, "y": 88}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.314Z", "elapsed": 1707, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:50.319Z", "elapsed": 1712, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.041Z", "elapsed": 4434, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.042Z", "elapsed": 4435, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.042Z", "elapsed": 4435, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.047Z", "elapsed": 4440, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.048Z", "elapsed": 4441, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.722Z", "elapsed": 5115, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 663008, "dataType": "object", "isArray": true, "searchQuery": "AI"}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.724Z", "elapsed": 5117, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 111, "processedCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.724Z", "elapsed": 5117, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 682}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.725Z", "elapsed": 5118, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.725Z", "elapsed": 5118, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.725Z", "elapsed": 5118, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 21903}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.726Z", "elapsed": 5119, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3059}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.735Z", "elapsed": 5128, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1131, "topWords": 50, "mostFrequent": "language"}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.736Z", "elapsed": 5129, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 11}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.737Z", "elapsed": 5130, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 15, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.737Z", "elapsed": 5130, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.737Z", "elapsed": 5130, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.738Z", "elapsed": 5131, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.738Z", "elapsed": 5131, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.740Z", "elapsed": 5133, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.747Z", "elapsed": 5140, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.748Z", "elapsed": 5141, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 11}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.749Z", "elapsed": 5142, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 707}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.841Z", "elapsed": 5234, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.842Z", "elapsed": 5235, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.847Z", "elapsed": 5240, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.848Z", "elapsed": 5241, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.991Z", "elapsed": 5384, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 358, "y": 340}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.997Z", "elapsed": 5390, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 404, "y": 188}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.998Z", "elapsed": 5391, "level": "DEBUG", "message": "Word positioned", "data": {"text": "systems", "x": 528, "y": 148}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.999Z", "elapsed": 5392, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agents", "x": 539, "y": 257}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.001Z", "elapsed": 5394, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 663, "y": 303}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.001Z", "elapsed": 5394, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llm", "x": 774, "y": 253}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.003Z", "elapsed": 5396, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 717, "y": 352}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.003Z", "elapsed": 5396, "level": "DEBUG", "message": "Word positioned", "data": {"text": "research", "x": 576, "y": 354}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.004Z", "elapsed": 5397, "level": "DEBUG", "message": "Word positioned", "data": {"text": "system", "x": 466, "y": 307}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.005Z", "elapsed": 5398, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agent", "x": 707, "y": 149}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.036Z", "elapsed": 5429, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 872, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.037Z", "elapsed": 5430, "level": "DEBUG", "message": "Word positioned", "data": {"text": "agentic", "x": 261, "y": 331}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.038Z", "elapsed": 5431, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 410, "y": 372}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.039Z", "elapsed": 5432, "level": "DEBUG", "message": "Word positioned", "data": {"text": "llms", "x": 339, "y": 241}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.040Z", "elapsed": 5433, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 782, "y": 132}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.041Z", "elapsed": 5434, "level": "DEBUG", "message": "Word positioned", "data": {"text": "engineering", "x": 270, "y": 190}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.042Z", "elapsed": 5435, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 649, "y": 206}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.042Z", "elapsed": 5435, "level": "DEBUG", "message": "Word positioned", "data": {"text": "human", "x": 823, "y": 370}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.047Z", "elapsed": 5440, "level": "DEBUG", "message": "Word positioned", "data": {"text": "benchmarks", "x": 847, "y": 149}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.048Z", "elapsed": 5441, "level": "DEBUG", "message": "Word positioned", "data": {"text": "their", "x": 498, "y": 397}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.049Z", "elapsed": 5442, "level": "DEBUG", "message": "Word positioned", "data": {"text": "memory", "x": 965, "y": 247}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.050Z", "elapsed": 5443, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 953, "y": 367}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.051Z", "elapsed": 5444, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 402, "y": 114}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.052Z", "elapsed": 5445, "level": "DEBUG", "message": "Word positioned", "data": {"text": "suggestions", "x": 181, "y": 134}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.052Z", "elapsed": 5445, "level": "DEBUG", "message": "Word positioned", "data": {"text": "code", "x": 605, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.053Z", "elapsed": 5446, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 259, "y": 248}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.053Z", "elapsed": 5446, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scientific", "x": 767, "y": 368}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.054Z", "elapsed": 5447, "level": "DEBUG", "message": "Word positioned", "data": {"text": "second", "x": 895, "y": 162}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.055Z", "elapsed": 5448, "level": "DEBUG", "message": "Word positioned", "data": {"text": "through", "x": 709, "y": 77}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.056Z", "elapsed": 5449, "level": "DEBUG", "message": "Word positioned", "data": {"text": "responses", "x": 630, "y": 394}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.057Z", "elapsed": 5450, "level": "DEBUG", "message": "Word positioned", "data": {"text": "evaluation", "x": 154, "y": 318}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.057Z", "elapsed": 5450, "level": "DEBUG", "message": "Word positioned", "data": {"text": "capabilities", "x": 190, "y": 86}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.059Z", "elapsed": 5452, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 951, "y": 118}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.065Z", "elapsed": 5458, "level": "DEBUG", "message": "Word positioned", "data": {"text": "github", "x": 540, "y": 450}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.066Z", "elapsed": 5459, "level": "DEBUG", "message": "Word positioned", "data": {"text": "music", "x": 1033, "y": 178}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.067Z", "elapsed": 5460, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tools", "x": 667, "y": 243}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.071Z", "elapsed": 5464, "level": "DEBUG", "message": "Word positioned", "data": {"text": "solutions", "x": 1051, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.073Z", "elapsed": 5466, "level": "DEBUG", "message": "Word positioned", "data": {"text": "user", "x": 215, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.074Z", "elapsed": 5467, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduce", "x": 1047, "y": 390}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.075Z", "elapsed": 5468, "level": "DEBUG", "message": "Word positioned", "data": {"text": "first", "x": 1084, "y": 259}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.076Z", "elapsed": 5469, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 636, "y": 445}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.077Z", "elapsed": 5470, "level": "DEBUG", "message": "Word positioned", "data": {"text": "interaction", "x": 315, "y": 415}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.081Z", "elapsed": 5474, "level": "DEBUG", "message": "Word positioned", "data": {"text": "provide", "x": 275, "y": 396}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.082Z", "elapsed": 5475, "level": "DEBUG", "message": "Word positioned", "data": {"text": "software", "x": 230, "y": 412}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.083Z", "elapsed": 5476, "level": "DEBUG", "message": "Word positioned", "data": {"text": "questions", "x": 81, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.084Z", "elapsed": 5477, "level": "DEBUG", "message": "Word positioned", "data": {"text": "users", "x": 461, "y": 436}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.085Z", "elapsed": 5478, "level": "DEBUG", "message": "Word positioned", "data": {"text": "knowledge", "x": 57, "y": 210}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.085Z", "elapsed": 5478, "level": "DEBUG", "message": "Word positioned", "data": {"text": "been", "x": 683, "y": 110}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.086Z", "elapsed": 5479, "level": "DEBUG", "message": "Word positioned", "data": {"text": "task", "x": 114, "y": 355}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.086Z", "elapsed": 5479, "level": "DEBUG", "message": "Word positioned", "data": {"text": "across", "x": 913, "y": 345}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.087Z", "elapsed": 5480, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.095Z", "elapsed": 5488, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.352Z", "elapsed": 5745, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.353Z", "elapsed": 5746, "level": "DEBUG", "message": "Updating word cloud colors", "data": {"colorScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:54:54.355Z", "elapsed": 5748, "level": "INFO", "message": "Word cloud colors updated", "data": {"colorScheme": "bright", "elementsUpdated": 50}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.003Z", "elapsed": 6396, "level": "DEBUG", "message": "Word hovered", "data": {"word": "their"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.062Z", "elapsed": 6455, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.063Z", "elapsed": 6456, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.066Z", "elapsed": 6459, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 15, "visibleCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.082Z", "elapsed": 6475, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.082Z", "elapsed": 6475, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.083Z", "elapsed": 6476, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 15, "visibleCount": 14}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.107Z", "elapsed": 6500, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.108Z", "elapsed": 6501, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.111Z", "elapsed": 6504, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 15, "visibleCount": 11}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.134Z", "elapsed": 6527, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.134Z", "elapsed": 6527, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.135Z", "elapsed": 6528, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.188Z", "elapsed": 6581, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.189Z", "elapsed": 6582, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.190Z", "elapsed": 6583, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.211Z", "elapsed": 6604, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.212Z", "elapsed": 6605, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.214Z", "elapsed": 6607, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.241Z", "elapsed": 6634, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.241Z", "elapsed": 6634, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.242Z", "elapsed": 6635, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.258Z", "elapsed": 6651, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.258Z", "elapsed": 6651, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.262Z", "elapsed": 6655, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.295Z", "elapsed": 6688, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.296Z", "elapsed": 6689, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.297Z", "elapsed": 6690, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.312Z", "elapsed": 6705, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.312Z", "elapsed": 6705, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.313Z", "elapsed": 6706, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 15, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.324Z", "elapsed": 6717, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.325Z", "elapsed": 6718, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.325Z", "elapsed": 6718, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 15, "visibleCount": 0}, "stack": null}, {"timestamp": "2025-06-05T18:54:55.332Z", "elapsed": 6725, "level": "DEBUG", "message": "Word hovered", "data": {"word": "language"}, "stack": null}], "stats": {"totalLogs": 203, "byLevel": {"DEBUG": 141, "INFO": 60, "WARN": 2, "ERROR": 0}, "errors": [], "warnings": [{"timestamp": "2025-06-05T18:54:49.938Z", "elapsed": 1331, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:54:53.738Z", "elapsed": 5131, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 7, "failedTests": 1, "totalErrors": 0, "totalLogs": 204, "screenshots": 3}}