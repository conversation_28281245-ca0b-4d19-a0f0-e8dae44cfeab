{"timestamp": "2025-06-05T18:33:28.745Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:33:34.485Z"}, {"name": "Loading Element Visible", "passed": true, "timestamp": "2025-06-05T18:33:34.651Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:33:34.666Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:33:34.674Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:33:38.303Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:33:39.086Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:33:40.795Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:33:40.869Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:33:33.068Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:33:33.166Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:33:33.299Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:33:33.301Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:33:33.311Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:33:33.313Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:33:33.314Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:33:33.316Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:33:33.319Z"}, {"type": "log", "text": "[INFO] Calling mockFetchPapers JSHandle@object", "timestamp": "2025-06-05T18:33:33.320Z"}, {"type": "log", "text": "[INFO] mockFetchPapers called - simulating API call JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:33.354Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:33:33.359Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:33:33.369Z"}, {"type": "log", "text": "[DEBUG] Mock API delay completed, returning papers JSHandle@object", "timestamp": "2025-06-05T18:33:34.876Z"}, {"type": "log", "text": "[INFO] Mock papers generated JSHandle@object", "timestamp": "2025-06-05T18:33:34.959Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:33:34.969Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:33:34.970Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:33:34.976Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:34.979Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:34.980Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:33:34.980Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:33:34.980Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:33:34.980Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:33:34.997Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:33:34.997Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:33:34.998Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:33:34.998Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:33:34.998Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:33:34.998Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:33:34.998Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:33:34.998Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:33:35.145Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:33:35.146Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:35.198Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:33:35.592Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.786Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.959Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.960Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.960Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.960Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.961Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.961Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.961Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.961Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.961Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.962Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.963Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.963Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.963Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.963Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.963Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.964Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.964Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.964Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.964Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.964Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.964Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.965Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.965Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.965Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.965Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.965Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.965Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.966Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.968Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.968Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.968Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.968Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:33:35.969Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:33:37.234Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:33:37.248Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:33:37.249Z"}, {"type": "log", "text": "[INFO] Calling mockFetchPapers JSHandle@object", "timestamp": "2025-06-05T18:33:37.249Z"}, {"type": "log", "text": "[INFO] mockFetchPapers called - simulating API call JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:37.260Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:33:38.549Z"}, {"type": "log", "text": "[DEBUG] Updating word cloud colors JSHandle@object", "timestamp": "2025-06-05T18:33:38.552Z"}, {"type": "log", "text": "[INFO] Word cloud colors updated JSHandle@object", "timestamp": "2025-06-05T18:33:38.553Z"}, {"type": "log", "text": "[DEBUG] Mock API delay completed, returning papers JSHandle@object", "timestamp": "2025-06-05T18:33:38.781Z"}, {"type": "log", "text": "[INFO] Mock papers generated JSHandle@object", "timestamp": "2025-06-05T18:33:38.782Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:33:38.782Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:33:38.782Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:33:38.782Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:38.782Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:38.783Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:33:38.783Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:33:38.783Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:33:38.783Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:33:38.783Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:33:38.783Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:33:38.784Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:33:38.784Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:33:38.869Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:33:39.085Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:33:39.085Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:33:39.086Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:33:39.210Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:33:39.211Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:39.211Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:33:39.212Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.212Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.212Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.212Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.212Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.213Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.213Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.213Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.213Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.213Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.213Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.214Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.214Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.214Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.214Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.214Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.215Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.215Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.215Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.216Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.216Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.216Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.216Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.216Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.216Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.217Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.217Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.218Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.218Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.218Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.219Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.219Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.219Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.219Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.219Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.221Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.223Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.224Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.225Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.226Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.226Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.226Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.227Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.227Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.227Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.227Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.227Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.227Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.228Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.228Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:33:39.229Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:33:39.229Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:33:39.229Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:39.766Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:39.768Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:39.768Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:39.945Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:39.945Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:39.946Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:39.985Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:39.990Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.001Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.034Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.035Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.045Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.065Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.067Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.067Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.150Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.151Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.151Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.165Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.175Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.179Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.195Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.196Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.196Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.216Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.217Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.218Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.246Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.246Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.247Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:33:40.267Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:33:40.268Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:33:40.269Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749148414175.png", "timestamp": "2025-06-05T18:33:34.418Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749148415013.png", "timestamp": "2025-06-05T18:33:36.897Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749148420877.png", "timestamp": "2025-06-05T18:33:41.348Z"}], "errors": [], "performance": {"pageLoad": 2921}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:33:33.093Z", "elapsed": 1, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:33:33.093Z"}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.264Z", "elapsed": 172, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.266Z", "elapsed": 174, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.267Z", "elapsed": 175, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.268Z", "elapsed": 176, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.269Z", "elapsed": 177, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.270Z", "elapsed": 178, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.276Z", "elapsed": 184, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.276Z", "elapsed": 184, "level": "INFO", "message": "Calling mockFetchPapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.277Z", "elapsed": 185, "level": "INFO", "message": "mockFetchPapers called - simulating API call", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.278Z", "elapsed": 186, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:33.278Z", "elapsed": 186, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.782Z", "elapsed": 1690, "level": "DEBUG", "message": "Mock API delay completed, returning papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.782Z", "elapsed": 1691, "level": "INFO", "message": "Mock papers generated", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.783Z", "elapsed": 1691, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 1514}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.784Z", "elapsed": 1692, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.784Z", "elapsed": 1692, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.786Z", "elapsed": 1694, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 4041}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.786Z", "elapsed": 1694, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 493}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.792Z", "elapsed": 1700, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 204, "topWords": 50, "mostFrequent": "learning"}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.793Z", "elapsed": 1701, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 9}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.794Z", "elapsed": 1702, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 15, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.795Z", "elapsed": 1703, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.796Z", "elapsed": 1704, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.797Z", "elapsed": 1705, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.798Z", "elapsed": 1706, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.798Z", "elapsed": 1706, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.802Z", "elapsed": 1710, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.803Z", "elapsed": 1711, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 8}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.809Z", "elapsed": 1717, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 1539}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.959Z", "elapsed": 1867, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.960Z", "elapsed": 1868, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.961Z", "elapsed": 1869, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:33:34.961Z", "elapsed": 1869, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.226Z", "elapsed": 2134, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 393, "y": 284}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.242Z", "elapsed": 2150, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 503, "y": 351}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.243Z", "elapsed": 2151, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 581, "y": 199}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.244Z", "elapsed": 2152, "level": "DEBUG", "message": "Word positioned", "data": {"text": "vision", "x": 595, "y": 350}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.245Z", "elapsed": 2153, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 434, "y": 255}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.246Z", "elapsed": 2154, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 635, "y": 379}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.247Z", "elapsed": 2155, "level": "DEBUG", "message": "Word positioned", "data": {"text": "quantum", "x": 676, "y": 237}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.252Z", "elapsed": 2160, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fine-tuning", "x": 302, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.253Z", "elapsed": 2161, "level": "DEBUG", "message": "Word positioned", "data": {"text": "speech", "x": 755, "y": 381}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.260Z", "elapsed": 2168, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 431, "y": 356}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.261Z", "elapsed": 2169, "level": "DEBUG", "message": "Word positioned", "data": {"text": "new", "x": 410, "y": 156}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.261Z", "elapsed": 2169, "level": "DEBUG", "message": "Word positioned", "data": {"text": "computer", "x": 738, "y": 159}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.262Z", "elapsed": 2170, "level": "DEBUG", "message": "Word positioned", "data": {"text": "privacy", "x": 818, "y": 132}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.278Z", "elapsed": 2186, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 683, "y": 184}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.279Z", "elapsed": 2187, "level": "DEBUG", "message": "Word positioned", "data": {"text": "adversarial", "x": 500, "y": 185}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.280Z", "elapsed": 2188, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 551, "y": 292}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.283Z", "elapsed": 2191, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 361, "y": 313}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.285Z", "elapsed": 2193, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comprehensive", "x": 370, "y": 226}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.286Z", "elapsed": 2194, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 707, "y": 126}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.293Z", "elapsed": 2201, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 499, "y": 250}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.294Z", "elapsed": 2202, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 650, "y": 140}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.295Z", "elapsed": 2203, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduces", "x": 766, "y": 340}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.295Z", "elapsed": 2203, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 695, "y": 380}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.296Z", "elapsed": 2204, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 619, "y": 158}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.298Z", "elapsed": 2206, "level": "DEBUG", "message": "Word positioned", "data": {"text": "contrastive", "x": 335, "y": 233}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.299Z", "elapsed": 2207, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 680, "y": 114}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.301Z", "elapsed": 2209, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 808, "y": 168}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.302Z", "elapsed": 2210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 850, "y": 288}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.302Z", "elapsed": 2210, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 613, "y": 308}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.303Z", "elapsed": 2211, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 417, "y": 187}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.303Z", "elapsed": 2211, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 302, "y": 208}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.308Z", "elapsed": 2216, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 517, "y": 227}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.309Z", "elapsed": 2217, "level": "DEBUG", "message": "Word positioned", "data": {"text": "edge", "x": 502, "y": 285}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.310Z", "elapsed": 2218, "level": "DEBUG", "message": "Word positioned", "data": {"text": "approach", "x": 612, "y": 265}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.311Z", "elapsed": 2219, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 845, "y": 109}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.312Z", "elapsed": 2220, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mechanisms", "x": 653, "y": 305}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.314Z", "elapsed": 2222, "level": "DEBUG", "message": "Word positioned", "data": {"text": "survey", "x": 729, "y": 280}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.315Z", "elapsed": 2223, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 484, "y": 317}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.317Z", "elapsed": 2225, "level": "DEBUG", "message": "Word positioned", "data": {"text": "federated", "x": 683, "y": 286}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.318Z", "elapsed": 2226, "level": "DEBUG", "message": "Word positioned", "data": {"text": "differential", "x": 362, "y": 144}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.319Z", "elapsed": 2227, "level": "DEBUG", "message": "Word positioned", "data": {"text": "graph", "x": 707, "y": 333}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.320Z", "elapsed": 2228, "level": "DEBUG", "message": "Word positioned", "data": {"text": "molecular", "x": 286, "y": 257}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.326Z", "elapsed": 2234, "level": "DEBUG", "message": "Word positioned", "data": {"text": "reinforcement", "x": 767, "y": 273}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.328Z", "elapsed": 2236, "level": "DEBUG", "message": "Word positioned", "data": {"text": "autonomous", "x": 775, "y": 189}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.329Z", "elapsed": 2237, "level": "DEBUG", "message": "Word positioned", "data": {"text": "meta-learning", "x": 537, "y": 375}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.329Z", "elapsed": 2237, "level": "DEBUG", "message": "Word positioned", "data": {"text": "classification", "x": 306, "y": 354}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.331Z", "elapsed": 2239, "level": "DEBUG", "message": "Word positioned", "data": {"text": "algorithms", "x": 822, "y": 367}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.332Z", "elapsed": 2240, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multimodal", "x": 837, "y": 210}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.333Z", "elapsed": 2241, "level": "DEBUG", "message": "Word positioned", "data": {"text": "robustness", "x": 811, "y": 313}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.334Z", "elapsed": 2242, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deep", "x": 877, "y": 324}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.350Z", "elapsed": 2258, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:35.364Z", "elapsed": 2272, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:37.229Z", "elapsed": 4137, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:37.229Z", "elapsed": 4137, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:37.230Z", "elapsed": 4138, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:37.230Z", "elapsed": 4138, "level": "INFO", "message": "Calling mockFetchPapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:37.231Z", "elapsed": 4139, "level": "INFO", "message": "mockFetchPapers called - simulating API call", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.544Z", "elapsed": 5452, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.545Z", "elapsed": 5453, "level": "DEBUG", "message": "Updating word cloud colors", "data": {"colorScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.547Z", "elapsed": 5455, "level": "INFO", "message": "Word cloud colors updated", "data": {"colorScheme": "bright", "elementsUpdated": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.732Z", "elapsed": 5640, "level": "DEBUG", "message": "Mock API delay completed, returning papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.733Z", "elapsed": 5641, "level": "INFO", "message": "Mock papers generated", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.733Z", "elapsed": 5641, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 15, "fetchTimeMs": 1504}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.734Z", "elapsed": 5642, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.735Z", "elapsed": 5643, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.735Z", "elapsed": 5643, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 4041}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.736Z", "elapsed": 5644, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 493}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.742Z", "elapsed": 5650, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 204, "topWords": 50, "mostFrequent": "learning"}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.743Z", "elapsed": 5651, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 8}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.744Z", "elapsed": 5652, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 15, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.744Z", "elapsed": 5652, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.745Z", "elapsed": 5653, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.746Z", "elapsed": 5654, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.747Z", "elapsed": 5655, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.748Z", "elapsed": 5657, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.760Z", "elapsed": 5668, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.761Z", "elapsed": 5669, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 17}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.763Z", "elapsed": 5671, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 1534}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.895Z", "elapsed": 5804, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.896Z", "elapsed": 5804, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.897Z", "elapsed": 5805, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "bright", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.898Z", "elapsed": 5806, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.033Z", "elapsed": 5941, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 326, "y": 160}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.034Z", "elapsed": 5942, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 436, "y": 253}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.036Z", "elapsed": 5944, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 509, "y": 334}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.043Z", "elapsed": 5951, "level": "DEBUG", "message": "Word positioned", "data": {"text": "vision", "x": 572, "y": 337}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.044Z", "elapsed": 5953, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 634, "y": 275}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.049Z", "elapsed": 5958, "level": "DEBUG", "message": "Word positioned", "data": {"text": "language", "x": 576, "y": 277}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.052Z", "elapsed": 5960, "level": "DEBUG", "message": "Word positioned", "data": {"text": "quantum", "x": 708, "y": 371}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.059Z", "elapsed": 5967, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fine-tuning", "x": 286, "y": 217}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.061Z", "elapsed": 5969, "level": "DEBUG", "message": "Word positioned", "data": {"text": "speech", "x": 786, "y": 253}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.062Z", "elapsed": 5970, "level": "DEBUG", "message": "Word positioned", "data": {"text": "tasks", "x": 368, "y": 300}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.063Z", "elapsed": 5971, "level": "DEBUG", "message": "Word positioned", "data": {"text": "new", "x": 798, "y": 279}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.063Z", "elapsed": 5971, "level": "DEBUG", "message": "Word positioned", "data": {"text": "computer", "x": 521, "y": 194}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.074Z", "elapsed": 5982, "level": "DEBUG", "message": "Word positioned", "data": {"text": "privacy", "x": 482, "y": 154}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.075Z", "elapsed": 5983, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 716, "y": 218}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.076Z", "elapsed": 5984, "level": "DEBUG", "message": "Word positioned", "data": {"text": "adversarial", "x": 698, "y": 133}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.077Z", "elapsed": 5985, "level": "DEBUG", "message": "Word positioned", "data": {"text": "transformer", "x": 756, "y": 300}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.078Z", "elapsed": 5986, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 399, "y": 222}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.080Z", "elapsed": 5988, "level": "DEBUG", "message": "Word positioned", "data": {"text": "comprehensive", "x": 443, "y": 281}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.082Z", "elapsed": 5990, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 504, "y": 253}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.082Z", "elapsed": 5990, "level": "DEBUG", "message": "Word positioned", "data": {"text": "large", "x": 664, "y": 333}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.083Z", "elapsed": 5991, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 337, "y": 260}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.093Z", "elapsed": 6001, "level": "DEBUG", "message": "Word positioned", "data": {"text": "introduces", "x": 611, "y": 344}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.094Z", "elapsed": 6002, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 644, "y": 201}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.095Z", "elapsed": 6004, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 624, "y": 224}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.097Z", "elapsed": 6005, "level": "DEBUG", "message": "Word positioned", "data": {"text": "contrastive", "x": 732, "y": 329}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.099Z", "elapsed": 6007, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 824, "y": 200}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.100Z", "elapsed": 6008, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 584, "y": 219}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.101Z", "elapsed": 6009, "level": "DEBUG", "message": "Word positioned", "data": {"text": "framework", "x": 777, "y": 203}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.102Z", "elapsed": 6010, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 467, "y": 217}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.103Z", "elapsed": 6011, "level": "DEBUG", "message": "Word positioned", "data": {"text": "diffusion", "x": 454, "y": 330}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.108Z", "elapsed": 6016, "level": "DEBUG", "message": "Word positioned", "data": {"text": "generation", "x": 312, "y": 307}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.109Z", "elapsed": 6017, "level": "DEBUG", "message": "Word positioned", "data": {"text": "architecture", "x": 385, "y": 169}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.111Z", "elapsed": 6019, "level": "DEBUG", "message": "Word positioned", "data": {"text": "edge", "x": 258, "y": 252}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.112Z", "elapsed": 6020, "level": "DEBUG", "message": "Word positioned", "data": {"text": "approach", "x": 645, "y": 387}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.112Z", "elapsed": 6020, "level": "DEBUG", "message": "Word positioned", "data": {"text": "attention", "x": 345, "y": 311}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.113Z", "elapsed": 6021, "level": "DEBUG", "message": "Word positioned", "data": {"text": "mechanisms", "x": 713, "y": 274}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.114Z", "elapsed": 6022, "level": "DEBUG", "message": "Word positioned", "data": {"text": "survey", "x": 459, "y": 360}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.116Z", "elapsed": 6024, "level": "DEBUG", "message": "Word positioned", "data": {"text": "applications", "x": 575, "y": 397}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.118Z", "elapsed": 6026, "level": "DEBUG", "message": "Word positioned", "data": {"text": "federated", "x": 444, "y": 185}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.119Z", "elapsed": 6027, "level": "DEBUG", "message": "Word positioned", "data": {"text": "differential", "x": 589, "y": 160}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.126Z", "elapsed": 6034, "level": "DEBUG", "message": "Word positioned", "data": {"text": "graph", "x": 854, "y": 151}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.127Z", "elapsed": 6035, "level": "DEBUG", "message": "Word positioned", "data": {"text": "molecular", "x": 631, "y": 157}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.128Z", "elapsed": 6036, "level": "DEBUG", "message": "Word positioned", "data": {"text": "reinforcement", "x": 867, "y": 215}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.130Z", "elapsed": 6038, "level": "DEBUG", "message": "Word positioned", "data": {"text": "autonomous", "x": 839, "y": 287}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.131Z", "elapsed": 6039, "level": "DEBUG", "message": "Word positioned", "data": {"text": "meta-learning", "x": 816, "y": 345}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.133Z", "elapsed": 6041, "level": "DEBUG", "message": "Word positioned", "data": {"text": "classification", "x": 730, "y": 393}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.134Z", "elapsed": 6042, "level": "DEBUG", "message": "Word positioned", "data": {"text": "algorithms", "x": 348, "y": 375}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.135Z", "elapsed": 6043, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multimodal", "x": 892, "y": 296}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.136Z", "elapsed": 6044, "level": "DEBUG", "message": "Word positioned", "data": {"text": "robustness", "x": 417, "y": 137}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.144Z", "elapsed": 6052, "level": "DEBUG", "message": "Word positioned", "data": {"text": "deep", "x": 549, "y": 365}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.145Z", "elapsed": 6053, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.151Z", "elapsed": 6059, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.759Z", "elapsed": 6667, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.761Z", "elapsed": 6669, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.762Z", "elapsed": 6670, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 15, "visibleCount": 15}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.911Z", "elapsed": 6819, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.912Z", "elapsed": 6820, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.913Z", "elapsed": 6821, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 15, "visibleCount": 10}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.965Z", "elapsed": 6873, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.968Z", "elapsed": 6876, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:33:39.969Z", "elapsed": 6877, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 15, "visibleCount": 5}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.024Z", "elapsed": 6932, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.025Z", "elapsed": 6933, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.027Z", "elapsed": 6935, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.056Z", "elapsed": 6964, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.057Z", "elapsed": 6965, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.059Z", "elapsed": 6967, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 15, "visibleCount": 3}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.100Z", "elapsed": 7008, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.101Z", "elapsed": 7009, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.102Z", "elapsed": 7010, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.164Z", "elapsed": 7072, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.165Z", "elapsed": 7073, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.166Z", "elapsed": 7074, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.186Z", "elapsed": 7094, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.191Z", "elapsed": 7099, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.192Z", "elapsed": 7100, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.211Z", "elapsed": 7119, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.212Z", "elapsed": 7120, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.213Z", "elapsed": 7121, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.236Z", "elapsed": 7144, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.242Z", "elapsed": 7150, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.243Z", "elapsed": 7151, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 15, "visibleCount": 2}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.262Z", "elapsed": 7170, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.263Z", "elapsed": 7171, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:33:40.264Z", "elapsed": 7172, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 15, "visibleCount": 2}, "stack": null}], "stats": {"totalLogs": 201, "byLevel": {"DEBUG": 141, "INFO": 58, "WARN": 2, "ERROR": 0}, "errors": [], "warnings": [{"timestamp": "2025-06-05T18:33:34.798Z", "elapsed": 1706, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:33:38.747Z", "elapsed": 5655, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 8, "failedTests": 0, "totalErrors": 0, "totalLogs": 202, "screenshots": 3}}