// Simple test script that can run without external dependencies
// This script opens the HTML file in a browser for manual testing

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class SimpleWordCloudTester {
    constructor() {
        this.htmlPath = path.resolve(__dirname, 'wordcloud.html');
        this.testResults = {
            timestamp: new Date().toISOString(),
            checks: [],
            recommendations: []
        };
    }

    checkFileExists() {
        console.log('🔍 Checking if wordcloud.html exists...');
        
        if (fs.existsSync(this.htmlPath)) {
            console.log('✅ wordcloud.html found');
            this.testResults.checks.push({
                name: 'File Exists',
                status: 'PASS',
                message: 'wordcloud.html file found'
            });
            return true;
        } else {
            console.log('❌ wordcloud.html not found');
            this.testResults.checks.push({
                name: 'File Exists',
                status: 'FAIL',
                message: 'wordcloud.html file not found'
            });
            return false;
        }
    }

    analyzeHtmlContent() {
        console.log('📄 Analyzing HTML content...');
        
        try {
            const content = fs.readFileSync(this.htmlPath, 'utf8');
            
            // Check for logging functionality
            const hasLogger = content.includes('class Logger');
            const hasErrorHandling = content.includes('try {') && content.includes('catch');
            const hasD3Integration = content.includes('d3.layout.cloud');
            const hasConsoleLogging = content.includes('console.log') || content.includes('logger.log');
            
            const checks = [
                { name: 'Logger Class', present: hasLogger, critical: true },
                { name: 'Error Handling', present: hasErrorHandling, critical: true },
                { name: 'D3.js Integration', present: hasD3Integration, critical: true },
                { name: 'Console Logging', present: hasConsoleLogging, critical: false }
            ];
            
            checks.forEach(check => {
                const status = check.present ? 'PASS' : 'FAIL';
                const icon = check.present ? '✅' : (check.critical ? '❌' : '⚠️');
                
                console.log(`${icon} ${check.name}: ${status}`);
                
                this.testResults.checks.push({
                    name: check.name,
                    status,
                    critical: check.critical,
                    message: check.present ? 'Feature detected' : 'Feature missing'
                });
            });
            
            // Check file size
            const stats = fs.statSync(this.htmlPath);
            const fileSizeKB = Math.round(stats.size / 1024);
            console.log(`📊 File size: ${fileSizeKB} KB`);
            
            this.testResults.checks.push({
                name: 'File Size',
                status: 'INFO',
                message: `${fileSizeKB} KB`
            });
            
            return true;
        } catch (error) {
            console.error('❌ Error analyzing HTML content:', error.message);
            this.testResults.checks.push({
                name: 'Content Analysis',
                status: 'FAIL',
                message: error.message
            });
            return false;
        }
    }

    generateRecommendations() {
        console.log('\n💡 Generating recommendations...');
        
        const failedChecks = this.testResults.checks.filter(check => check.status === 'FAIL');
        
        if (failedChecks.length === 0) {
            console.log('✅ All checks passed! The application appears to be ready for headless testing.');
            this.testResults.recommendations.push('Application is ready for headless testing with Puppeteer');
        } else {
            console.log('⚠️ Some issues found:');
            failedChecks.forEach(check => {
                console.log(`   - ${check.name}: ${check.message}`);
            });
        }
        
        // General recommendations
        const recommendations = [
            'Install Node.js and npm if not already installed',
            'Run "npm install" to install Puppeteer for headless testing',
            'Use "npm test" to run the full headless test suite',
            'Check browser console for any JavaScript errors when running manually',
            'Test in different browsers to ensure compatibility',
            'Verify that external CDN resources (D3.js, Tailwind CSS) load properly'
        ];
        
        console.log('\n📋 General recommendations:');
        recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
            this.testResults.recommendations.push(rec);
        });
    }

    openInBrowser() {
        console.log('\n🌐 Attempting to open in default browser...');
        
        const fileUrl = `file://${this.htmlPath}`;
        
        try {
            let command;
            let args;
            
            switch (process.platform) {
                case 'win32':
                    command = 'cmd';
                    args = ['/c', 'start', fileUrl];
                    break;
                case 'darwin':
                    command = 'open';
                    args = [fileUrl];
                    break;
                default:
                    command = 'xdg-open';
                    args = [fileUrl];
                    break;
            }
            
            const child = spawn(command, args, { detached: true, stdio: 'ignore' });
            child.unref();
            
            console.log('✅ Opened in browser. Please check for:');
            console.log('   - Page loads without errors');
            console.log('   - Word cloud renders properly');
            console.log('   - All interactive elements work');
            console.log('   - Console shows logging output');
            console.log('   - Download logs button is present');
            
            this.testResults.checks.push({
                name: 'Browser Launch',
                status: 'PASS',
                message: 'Successfully opened in browser'
            });
            
        } catch (error) {
            console.error('❌ Failed to open in browser:', error.message);
            console.log(`📋 Manual testing: Open ${fileUrl} in your browser`);
            
            this.testResults.checks.push({
                name: 'Browser Launch',
                status: 'FAIL',
                message: error.message
            });
        }
    }

    saveReport() {
        const reportPath = `simple-test-report-${Date.now()}.json`;
        
        try {
            fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
            console.log(`\n📊 Test report saved: ${reportPath}`);
        } catch (error) {
            console.error('❌ Failed to save report:', error.message);
        }
    }

    run() {
        console.log('🚀 Starting simple wordcloud.html test...\n');
        
        if (!this.checkFileExists()) {
            console.log('\n💥 Cannot proceed without the HTML file');
            return;
        }
        
        this.analyzeHtmlContent();
        this.generateRecommendations();
        this.openInBrowser();
        this.saveReport();
        
        console.log('\n🎉 Simple test completed!');
        console.log('📋 For comprehensive headless testing, install dependencies and run:');
        console.log('   npm install');
        console.log('   npm test');
    }
}

// Run the test if this file is executed directly
if (require.main === module) {
    const tester = new SimpleWordCloudTester();
    tester.run();
}

module.exports = SimpleWordCloudTester;
