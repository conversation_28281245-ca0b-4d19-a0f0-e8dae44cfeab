const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class ComprehensiveWordCloudTester {
    constructor() {
        this.browser = null;
        this.page = null;
        this.testResults = {
            timestamp: new Date().toISOString(),
            tests: [],
            issues: [],
            successes: []
        };
    }

    async initialize() {
        console.log('🚀 Starting comprehensive functionality test...');

        this.browser = await puppeteer.launch({
            headless: false, // Run in visible mode to see what's happening
            args: ['--no-sandbox', '--disable-setuid-sandbox'],
            slowMo: 100 // Slow down for better observation
        });

        this.page = await this.browser.newPage();
        await this.page.setViewport({ width: 1280, height: 720 });

        // Capture console logs
        this.page.on('console', (msg) => {
            if (msg.type() === 'error') {
                this.testResults.issues.push(`Console Error: ${msg.text()}`);
            }
        });
    }

    async loadPage() {
        console.log('📄 Loading wordcloud.html...');
        const htmlPath = path.resolve(__dirname, 'wordcloud.html');
        await this.page.goto(`file://${htmlPath}`, { waitUntil: 'networkidle0' });

        // Wait for data to load
        await this.page.waitForFunction(() => {
            const loading = document.getElementById('loading');
            return loading && loading.classList.contains('hidden');
        }, { timeout: 15000 });

        console.log('✅ Page loaded and data fetched');
    }

    async testRealAPIData() {
        console.log('🔍 Testing real API data...');

        // Check if we have real papers with real authors
        const papersData = await this.page.evaluate(() => {
            const paperCards = document.querySelectorAll('.paper-card');
            const papers = [];

            paperCards.forEach(card => {
                const title = card.querySelector('h3')?.textContent || '';
                const authors = card.querySelector('.text-sm.text-gray-500')?.textContent || '';
                const link = card.querySelector('a[href]')?.href || '';

                papers.push({ title, authors, link });
            });

            return papers;
        });

        if (papersData.length === 0) {
            this.testResults.issues.push('❌ No papers found on page');
            return false;
        }

        // Check for mock data indicators
        const hasMockData = papersData.some(paper =>
            paper.authors.includes('Unknown Authors') ||
            paper.link.includes('#') ||
            paper.title.includes('Untitled Paper')
        );

        if (hasMockData) {
            this.testResults.issues.push('❌ Still displaying mock/fallback data');
            console.log('❌ Found mock data in papers:', papersData.filter(p =>
                p.authors.includes('Unknown Authors') || p.link.includes('#')
            ));
            return false;
        }

        // Check for real Hugging Face URLs
        const hasRealLinks = papersData.every(paper =>
            paper.link.includes('huggingface.co/papers/')
        );

        if (!hasRealLinks) {
            this.testResults.issues.push('❌ Paper links are not pointing to Hugging Face');
            return false;
        }

        console.log('✅ Real API data confirmed:', {
            paperCount: papersData.length,
            samplePaper: papersData[0]
        });

        this.testResults.successes.push('✅ Real API data loaded successfully');
        return true;
    }

    async testWordClickFunctionality() {
        console.log('🖱️ Testing word click functionality...');

        // Wait for word cloud to render
        await this.page.waitForSelector('.word', { timeout: 5000 });

        // Get initial paper count
        const initialVisiblePapers = await this.page.evaluate(() => {
            return document.querySelectorAll('.paper-card:not([style*="display: none"])').length;
        });

        // Click on a word
        await this.page.click('.word');

        // Wait a moment for filtering
        await this.page.waitForTimeout(1000);

        // Check if search input was updated
        const searchValue = await this.page.$eval('#paper-search', el => el.value);

        if (!searchValue) {
            this.testResults.issues.push('❌ Word click did not update search input');
            return false;
        }

        // Check if papers were filtered
        const filteredVisiblePapers = await this.page.evaluate(() => {
            return document.querySelectorAll('.paper-card:not([style*="display: none"])').length;
        });

        if (filteredVisiblePapers === initialVisiblePapers) {
            this.testResults.issues.push('❌ Word click did not filter papers');
            return false;
        }

        console.log('✅ Word click functionality working:', {
            searchTerm: searchValue,
            initialPapers: initialVisiblePapers,
            filteredPapers: filteredVisiblePapers
        });

        this.testResults.successes.push('✅ Word click filtering works correctly');
        return true;
    }

    async testHighlightToggle() {
        console.log('🎨 Testing highlight toggle functionality...');

        // Clear any existing search
        await this.page.$eval('#paper-search', el => el.value = '');
        await this.page.evaluate(() => {
            // Trigger input event to clear filters
            const searchInput = document.getElementById('paper-search');
            if (searchInput) {
                searchInput.dispatchEvent(new Event('input'));
            }
        });

        // Check initial state
        const initialToggleState = await this.page.$eval('#highlight-toggle', el => el.checked);

        // Toggle the highlight mode by clicking the label (more reliable)
        await this.page.click('label[for="highlight-toggle"]');
        await this.page.waitForTimeout(500);

        // Check if state changed
        const newToggleState = await this.page.$eval('#highlight-toggle', el => el.checked);

        if (initialToggleState === newToggleState) {
            // Try clicking the checkbox directly
            await this.page.evaluate(() => {
                const toggle = document.getElementById('highlight-toggle');
                toggle.click();
            });
            await this.page.waitForTimeout(500);

            const finalToggleState = await this.page.$eval('#highlight-toggle', el => el.checked);
            if (initialToggleState === finalToggleState) {
                this.testResults.issues.push('❌ Highlight toggle did not change state');
                return false;
            }
        }

        // Check if highlight-mode class was added/removed
        const hasHighlightMode = await this.page.evaluate(() => {
            return document.getElementById('word-cloud').classList.contains('highlight-mode');
        });

        if (hasHighlightMode === initialToggleState) {
            this.testResults.issues.push('❌ Highlight mode class not toggled correctly');
            return false;
        }

        console.log('✅ Highlight toggle working:', {
            initialState: initialToggleState,
            newState: newToggleState,
            highlightModeActive: hasHighlightMode
        });

        this.testResults.successes.push('✅ Highlight toggle functionality works');
        return true;
    }

    async testColorSchemeChanges() {
        console.log('🌈 Testing color scheme changes...');

        // Test different color schemes
        const schemes = ['bright', 'pastel', 'monochrome', 'default'];

        for (const scheme of schemes) {
            await this.page.select('#color-scheme', scheme);
            await this.page.waitForTimeout(500);

            // Check if colors actually changed
            const wordColors = await this.page.evaluate(() => {
                const words = document.querySelectorAll('.word');
                return Array.from(words).slice(0, 3).map(word =>
                    window.getComputedStyle(word).color
                );
            });

            console.log(`  Color scheme "${scheme}":`, wordColors[0]);
        }

        this.testResults.successes.push('✅ Color scheme changes work');
        return true;
    }

    async testPaperLinks() {
        console.log('🔗 Testing paper links...');

        // Get a paper link
        const paperLink = await this.page.$eval('.paper-card a[href]', el => el.href);

        if (!paperLink.includes('huggingface.co/papers/')) {
            this.testResults.issues.push('❌ Paper links do not point to Hugging Face');
            return false;
        }

        console.log('✅ Paper links are correct:', paperLink);
        this.testResults.successes.push('✅ Paper links point to real Hugging Face papers');
        return true;
    }

    async generateReport() {
        const reportPath = `comprehensive-test-report-${Date.now()}.json`;

        this.testResults.summary = {
            totalSuccesses: this.testResults.successes.length,
            totalIssues: this.testResults.issues.length,
            overallStatus: this.testResults.issues.length === 0 ? 'PASS' : 'FAIL'
        };

        fs.writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));

        console.log('\n📊 COMPREHENSIVE TEST RESULTS:');
        console.log(`✅ Successes: ${this.testResults.successes.length}`);
        console.log(`❌ Issues: ${this.testResults.issues.length}`);
        console.log(`📄 Report saved: ${reportPath}`);

        if (this.testResults.issues.length > 0) {
            console.log('\n❌ ISSUES FOUND:');
            this.testResults.issues.forEach(issue => console.log(`  ${issue}`));
        }

        if (this.testResults.successes.length > 0) {
            console.log('\n✅ SUCCESSES:');
            this.testResults.successes.forEach(success => console.log(`  ${success}`));
        }

        return this.testResults.summary.overallStatus === 'PASS';
    }

    async cleanup() {
        if (this.browser) {
            await this.browser.close();
        }
    }

    async run() {
        try {
            await this.initialize();
            await this.loadPage();

            await this.testRealAPIData();
            await this.testWordClickFunctionality();
            await this.testHighlightToggle();
            await this.testColorSchemeChanges();
            await this.testPaperLinks();

            const success = await this.generateReport();

            if (success) {
                console.log('\n🎉 ALL TESTS PASSED! The application is working correctly.');
            } else {
                console.log('\n💥 SOME TESTS FAILED! Please review the issues above.');
            }

            return success;

        } catch (error) {
            console.error('💥 Test execution failed:', error.message);
            this.testResults.issues.push(`Test execution error: ${error.message}`);
            await this.generateReport();
            return false;
        } finally {
            await this.cleanup();
        }
    }
}

// Run the comprehensive test
if (require.main === module) {
    const tester = new ComprehensiveWordCloudTester();
    tester.run()
        .then((success) => {
            process.exit(success ? 0 : 1);
        })
        .catch((error) => {
            console.error('Test runner failed:', error);
            process.exit(1);
        });
}

module.exports = ComprehensiveWordCloudTester;
