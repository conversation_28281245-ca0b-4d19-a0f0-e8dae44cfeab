{"timestamp": "2025-06-05T18:42:13.102Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:42:17.936Z"}, {"name": "Loading Element Visible", "passed": false, "timestamp": "2025-06-05T18:42:18.120Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:42:18.183Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:42:18.186Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": true, "timestamp": "2025-06-05T18:42:20.894Z"}, {"name": "Color Scheme Change", "passed": true, "timestamp": "2025-06-05T18:42:21.464Z"}, {"name": "Search Functionality", "passed": true, "timestamp": "2025-06-05T18:42:22.288Z"}, {"name": "Download Logs Button", "passed": true, "timestamp": "2025-06-05T18:42:22.331Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:42:14.818Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:42:15.134Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:42:15.249Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:42:15.266Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:42:15.266Z"}, {"type": "log", "text": "[INFO] External dependencies verified successfully JSHandle@object", "timestamp": "2025-06-05T18:42:15.267Z"}, {"type": "log", "text": "[INFO] Starting application initialization JSHandle@object", "timestamp": "2025-06-05T18:42:15.267Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:42:15.267Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:42:15.268Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:42:15.269Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:42:15.269Z"}, {"type": "log", "text": "[INFO] Setting up event listeners JSHandle@object", "timestamp": "2025-06-05T18:42:15.286Z"}, {"type": "log", "text": "[INFO] Application initialization completed JSHandle@object", "timestamp": "2025-06-05T18:42:15.286Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:42:16.300Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:42:16.383Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:42:16.421Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:42:16.465Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:42:16.479Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:16.480Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:16.480Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:42:16.482Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:42:16.482Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:42:16.544Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:42:16.621Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:42:16.668Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:42:16.669Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:42:16.672Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:42:16.687Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:42:16.695Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:42:16.695Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:42:16.696Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:42:16.699Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:42:16.699Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:16.699Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:42:16.699Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.702Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.702Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.703Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.704Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.705Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.706Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.706Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.709Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.711Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.715Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.715Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.716Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.717Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.717Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.717Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.717Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.717Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.718Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.718Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.718Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.719Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.719Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.719Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.719Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.722Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.725Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.726Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.727Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.727Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:16.728Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:42:16.728Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:42:16.728Z"}, {"type": "log", "text": "[INFO] Refresh button clicked J<PERSON><PERSON><PERSON><PERSON>@object", "timestamp": "2025-06-05T18:42:19.854Z"}, {"type": "log", "text": "[INFO] Starting to fetch papers JSHandle@object", "timestamp": "2025-06-05T18:42:19.867Z"}, {"type": "log", "text": "[DEBUG] Updating UI to loading state JSHandle@object", "timestamp": "2025-06-05T18:42:19.868Z"}, {"type": "log", "text": "[INFO] Calling fetchHuggingFacePapers JSHandle@object", "timestamp": "2025-06-05T18:42:19.868Z"}, {"type": "log", "text": "[INFO] Fetching real papers from Hugging Face API JSHandle@object", "timestamp": "2025-06-05T18:42:19.869Z"}, {"type": "log", "text": "[INFO] Raw API response received JSHandle@object", "timestamp": "2025-06-05T18:42:20.249Z"}, {"type": "log", "text": "[INFO] Papers processed successfully JSHandle@object", "timestamp": "2025-06-05T18:42:20.249Z"}, {"type": "log", "text": "[INFO] Papers fetched successfully JSHandle@object", "timestamp": "2025-06-05T18:42:20.250Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:42:20.251Z"}, {"type": "log", "text": "[INFO] Processing papers for word cloud JSHandle@object", "timestamp": "2025-06-05T18:42:20.252Z"}, {"type": "log", "text": "[DEBUG] Text extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:20.252Z"}, {"type": "log", "text": "[DEBUG] Word extraction completed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:20.253Z"}, {"type": "log", "text": "[INFO] Word cloud processing completed JSHandle@object", "timestamp": "2025-06-05T18:42:20.267Z"}, {"type": "log", "text": "[INFO] Word cloud data processed JSHandle@object", "timestamp": "2025-06-05T18:42:20.268Z"}, {"type": "log", "text": "[DEBUG] Updating UI badges JSHandle@object", "timestamp": "2025-06-05T18:42:20.268Z"}, {"type": "log", "text": "[INFO] Rendering UI components JSHandle@object", "timestamp": "2025-06-05T18:42:20.268Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:42:20.268Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:42:20.268Z"}, {"type": "warning", "text": "[WARN] Container has zero dimensions, retrying in 100ms JSHandle@object", "timestamp": "2025-06-05T18:42:20.269Z"}, {"type": "log", "text": "[INFO] Rendering papers list JSHandle@object", "timestamp": "2025-06-05T18:42:20.269Z"}, {"type": "log", "text": "[INFO] Papers list rendered successfully JSHandle@object", "timestamp": "2025-06-05T18:42:20.270Z"}, {"type": "log", "text": "[INFO] UI rendering completed JSHandle@object", "timestamp": "2025-06-05T18:42:20.270Z"}, {"type": "log", "text": "[INFO] fetchPapers completed successfully JSHandle@object", "timestamp": "2025-06-05T18:42:20.271Z"}, {"type": "log", "text": "[INFO] Starting word cloud rendering JSHandle@object", "timestamp": "2025-06-05T18:42:20.382Z"}, {"type": "log", "text": "[DEBUG] Container dimensions JSHandle@object", "timestamp": "2025-06-05T18:42:20.384Z"}, {"type": "log", "text": "[DEBUG] Using color scheme JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:20.384Z"}, {"type": "log", "text": "[DEBUG] Initializing D3 word cloud layout JSHandle@object", "timestamp": "2025-06-05T18:42:20.384Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.486Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.489Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.516Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.531Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.550Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.595Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.596Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.600Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.602Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.603Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.603Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.603Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.605Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.605Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.605Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.605Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.612Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.612Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.612Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.613Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.613Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.615Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.615Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.615Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.616Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.617Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.617Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.617Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.618Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.618Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.618Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.618Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.618Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.619Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.619Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.619Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.619Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.619Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.620Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.620Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.620Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.620Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.620Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.621Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.621Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.621Z"}, {"type": "log", "text": "[DEBUG] Word positioned JSHandle@object", "timestamp": "2025-06-05T18:42:20.621Z"}, {"type": "log", "text": "[INFO] Drawing word cloud JSHandle@object", "timestamp": "2025-06-05T18:42:20.621Z"}, {"type": "log", "text": "[INFO] Word cloud rendering completed successfully JSHandle@object", "timestamp": "2025-06-05T18:42:20.622Z"}, {"type": "log", "text": "[INFO] Color scheme changed JSHandle@object", "timestamp": "2025-06-05T18:42:20.937Z"}, {"type": "log", "text": "[DEBUG] Updating word cloud colors JSHandle@object", "timestamp": "2025-06-05T18:42:20.938Z"}, {"type": "log", "text": "[INFO] Word cloud colors updated JSHandle@object", "timestamp": "2025-06-05T18:42:20.946Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.520Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.538Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.548Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.555Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.558Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.565Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.583Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.583Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.583Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.590Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.616Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.618Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.627Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.628Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.629Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.645Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.656Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.659Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.665Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.667Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.668Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.677Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.678Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.678Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.689Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.692Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.695Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.704Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.704Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.705Z"}, {"type": "log", "text": "[DEBUG] Search input changed JSH<PERSON>le@object", "timestamp": "2025-06-05T18:42:21.733Z"}, {"type": "log", "text": "[DEBUG] Filtering papers JSHandle@object", "timestamp": "2025-06-05T18:42:21.735Z"}, {"type": "log", "text": "[INFO] Papers filtered JSHandle@object", "timestamp": "2025-06-05T18:42:21.755Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749148937198.png", "timestamp": "2025-06-05T18:42:17.904Z"}, {"name": "02-data-loaded", "path": "screenshot-02-data-loaded-1749148938264.png", "timestamp": "2025-06-05T18:42:19.045Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749148942332.png", "timestamp": "2025-06-05T18:42:22.594Z"}], "errors": [], "performance": {"pageLoad": 3111}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:42:15.121Z", "elapsed": 0, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:42:15.121Z"}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.232Z", "elapsed": 111, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.233Z", "elapsed": 112, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.234Z", "elapsed": 113, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.234Z", "elapsed": 113, "level": "INFO", "message": "External dependencies verified successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.234Z", "elapsed": 113, "level": "INFO", "message": "Starting application initialization", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.235Z", "elapsed": 114, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.235Z", "elapsed": 114, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.235Z", "elapsed": 114, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.235Z", "elapsed": 115, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.237Z", "elapsed": 116, "level": "INFO", "message": "Setting up event listeners", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:15.237Z", "elapsed": 116, "level": "INFO", "message": "Application initialization completed", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.197Z", "elapsed": 1076, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 706996, "dataType": "object", "isArray": true, "searchQuery": "neural"}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.199Z", "elapsed": 1078, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 136, "processedCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.200Z", "elapsed": 1079, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 20, "fetchTimeMs": 965}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.200Z", "elapsed": 1079, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.201Z", "elapsed": 1080, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.201Z", "elapsed": 1080, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 24936}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.202Z", "elapsed": 1081, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3525}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.205Z", "elapsed": 1084, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1094, "topWords": 50, "mostFrequent": "neural"}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.206Z", "elapsed": 1085, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 6}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.208Z", "elapsed": 1087, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 20, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.209Z", "elapsed": 1088, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.210Z", "elapsed": 1089, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.212Z", "elapsed": 1091, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.213Z", "elapsed": 1092, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.213Z", "elapsed": 1092, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.217Z", "elapsed": 1096, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.217Z", "elapsed": 1096, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 8}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.218Z", "elapsed": 1097, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 983}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.338Z", "elapsed": 1217, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.340Z", "elapsed": 1219, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.342Z", "elapsed": 1221, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.342Z", "elapsed": 1221, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.505Z", "elapsed": 1384, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 746, "y": 263}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.511Z", "elapsed": 1390, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 623, "y": 227}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.511Z", "elapsed": 1390, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fields", "x": 496, "y": 308}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.512Z", "elapsed": 1391, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 849, "y": 206}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.514Z", "elapsed": 1393, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 793, "y": 115}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.515Z", "elapsed": 1394, "level": "DEBUG", "message": "Word positioned", "data": {"text": "radiance", "x": 565, "y": 341}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.516Z", "elapsed": 1395, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nerf", "x": 386, "y": 230}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.516Z", "elapsed": 1395, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 773, "y": 371}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.517Z", "elapsed": 1396, "level": "DEBUG", "message": "Word positioned", "data": {"text": "reconstruction", "x": 630, "y": 140}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.518Z", "elapsed": 1397, "level": "DEBUG", "message": "Word positioned", "data": {"text": "dataset", "x": 680, "y": 347}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.518Z", "elapsed": 1397, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 675, "y": 94}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.518Z", "elapsed": 1397, "level": "DEBUG", "message": "Word positioned", "data": {"text": "rendering", "x": 324, "y": 360}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.519Z", "elapsed": 1398, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 972, "y": 180}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.520Z", "elapsed": 1399, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 897, "y": 282}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.521Z", "elapsed": 1400, "level": "DEBUG", "message": "Word positioned", "data": {"text": "approach", "x": 395, "y": 174}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.521Z", "elapsed": 1400, "level": "DEBUG", "message": "Word positioned", "data": {"text": "depth", "x": 276, "y": 241}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.522Z", "elapsed": 1401, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 519, "y": 102}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.524Z", "elapsed": 1403, "level": "DEBUG", "message": "Word positioned", "data": {"text": "video", "x": 991, "y": 307}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.525Z", "elapsed": 1404, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scene", "x": 431, "y": 349}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.526Z", "elapsed": 1405, "level": "DEBUG", "message": "Word positioned", "data": {"text": "method", "x": 227, "y": 138}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.527Z", "elapsed": 1406, "level": "DEBUG", "message": "Word positioned", "data": {"text": "using", "x": 395, "y": 129}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.528Z", "elapsed": 1407, "level": "DEBUG", "message": "Word positioned", "data": {"text": "images", "x": 826, "y": 366}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.528Z", "elapsed": 1407, "level": "DEBUG", "message": "Word positioned", "data": {"text": "research", "x": 962, "y": 398}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.529Z", "elapsed": 1408, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 516, "y": 221}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.529Z", "elapsed": 1408, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 487, "y": 385}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.532Z", "elapsed": 1411, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 674, "y": 394}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.533Z", "elapsed": 1412, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 620, "y": 451}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.535Z", "elapsed": 1414, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 211, "y": 280}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.536Z", "elapsed": 1415, "level": "DEBUG", "message": "Word positioned", "data": {"text": "methods", "x": 1062, "y": 324}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.537Z", "elapsed": 1416, "level": "DEBUG", "message": "Word positioned", "data": {"text": "temporal", "x": 255, "y": 382}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.538Z", "elapsed": 1417, "level": "DEBUG", "message": "Word positioned", "data": {"text": "shape", "x": 678, "y": 297}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.538Z", "elapsed": 1417, "level": "DEBUG", "message": "Word positioned", "data": {"text": "time", "x": 866, "y": 419}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.553Z", "elapsed": 1432, "level": "DEBUG", "message": "Word positioned", "data": {"text": "different", "x": 170, "y": 158}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.553Z", "elapsed": 1432, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 728, "y": 192}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.554Z", "elapsed": 1433, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learned", "x": 1079, "y": 169}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.555Z", "elapsed": 1434, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multiple", "x": 352, "y": 69}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.557Z", "elapsed": 1436, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 620, "y": 43}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.558Z", "elapsed": 1437, "level": "DEBUG", "message": "Word positioned", "data": {"text": "via", "x": 377, "y": 270}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.559Z", "elapsed": 1438, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scenes", "x": 953, "y": 130}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.560Z", "elapsed": 1439, "level": "DEBUG", "message": "Word positioned", "data": {"text": "work", "x": 129, "y": 171}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.562Z", "elapsed": 1441, "level": "DEBUG", "message": "Word positioned", "data": {"text": "changes", "x": 1073, "y": 438}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.562Z", "elapsed": 1441, "level": "DEBUG", "message": "Word positioned", "data": {"text": "over", "x": 664, "y": 437}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.564Z", "elapsed": 1443, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representations", "x": 134, "y": 44}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.565Z", "elapsed": 1444, "level": "DEBUG", "message": "Word positioned", "data": {"text": "optimization", "x": 958, "y": 79}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.566Z", "elapsed": 1445, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nerfs", "x": 612, "y": 175}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.567Z", "elapsed": 1446, "level": "DEBUG", "message": "Word positioned", "data": {"text": "existing", "x": 906, "y": 32}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.567Z", "elapsed": 1446, "level": "DEBUG", "message": "Word positioned", "data": {"text": "demonstrate", "x": 1118, "y": 324}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.568Z", "elapsed": 1447, "level": "DEBUG", "message": "Word positioned", "data": {"text": "dreameditor", "x": 92, "y": 192}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.569Z", "elapsed": 1448, "level": "DEBUG", "message": "Word positioned", "data": {"text": "geometry", "x": 54, "y": 150}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.570Z", "elapsed": 1449, "level": "DEBUG", "message": "Word positioned", "data": {"text": "text", "x": 457, "y": 207}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.571Z", "elapsed": 1450, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:16.577Z", "elapsed": 1456, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:19.836Z", "elapsed": 4715, "level": "INFO", "message": "Refresh button clicked", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:19.837Z", "elapsed": 4716, "level": "INFO", "message": "Starting to fetch papers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:19.837Z", "elapsed": 4716, "level": "DEBUG", "message": "Updating UI to loading state", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:19.838Z", "elapsed": 4717, "level": "INFO", "message": "Calling fetchHuggingFacePapers", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:19.838Z", "elapsed": 4717, "level": "INFO", "message": "Fetching real papers from Hugging Face API", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.246Z", "elapsed": 5125, "level": "INFO", "message": "Raw API response received", "data": {"responseSize": 706996, "dataType": "object", "isArray": true, "searchQuery": "neural"}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.248Z", "elapsed": 5127, "level": "INFO", "message": "Papers processed successfully", "data": {"originalCount": 136, "processedCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.249Z", "elapsed": 5128, "level": "INFO", "message": "Papers fetched successfully", "data": {"paperCount": 20, "fetchTimeMs": 412}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.249Z", "elapsed": 5128, "level": "INFO", "message": "Processing papers for word cloud", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.249Z", "elapsed": 5128, "level": "INFO", "message": "Processing papers for word cloud", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.250Z", "elapsed": 5129, "level": "DEBUG", "message": "Text extraction completed", "data": {"textLength": 24936}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.251Z", "elapsed": 5130, "level": "DEBUG", "message": "Word extraction completed", "data": {"totalWords": 3525}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.255Z", "elapsed": 5134, "level": "INFO", "message": "Word cloud processing completed", "data": {"uniqueWords": 1094, "topWords": 50, "mostFrequent": "neural"}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.260Z", "elapsed": 5139, "level": "INFO", "message": "Word cloud data processed", "data": {"wordCount": 50, "processTimeMs": 10}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.260Z", "elapsed": 5139, "level": "DEBUG", "message": "Updating UI badges", "data": {"paperCount": 20, "wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.261Z", "elapsed": 5140, "level": "INFO", "message": "Rendering UI components", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.261Z", "elapsed": 5140, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.262Z", "elapsed": 5141, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 0, "height": 0}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.263Z", "elapsed": 5142, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.264Z", "elapsed": 5143, "level": "INFO", "message": "Rendering papers list", "data": {"paperCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.268Z", "elapsed": 5147, "level": "INFO", "message": "Papers list rendered successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.269Z", "elapsed": 5148, "level": "INFO", "message": "UI rendering completed", "data": {"renderTimeMs": 8}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.270Z", "elapsed": 5149, "level": "INFO", "message": "fetchPapers completed successfully", "data": {"totalTimeMs": 433}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.379Z", "elapsed": 5258, "level": "INFO", "message": "Starting word cloud rendering", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.379Z", "elapsed": 5258, "level": "DEBUG", "message": "Container dimensions", "data": {"width": 1168, "height": 504}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.380Z", "elapsed": 5259, "level": "DEBUG", "message": "Using color scheme", "data": {"colorScheme": "default", "colorCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.380Z", "elapsed": 5259, "level": "DEBUG", "message": "Initializing D3 word cloud layout", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.484Z", "elapsed": 5363, "level": "DEBUG", "message": "Word positioned", "data": {"text": "neural", "x": 408, "y": 126}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.485Z", "elapsed": 5364, "level": "DEBUG", "message": "Word positioned", "data": {"text": "model", "x": 532, "y": 229}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.485Z", "elapsed": 5364, "level": "DEBUG", "message": "Word positioned", "data": {"text": "fields", "x": 304, "y": 169}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.486Z", "elapsed": 5365, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learning", "x": 691, "y": 274}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.487Z", "elapsed": 5366, "level": "DEBUG", "message": "Word positioned", "data": {"text": "translation", "x": 431, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.489Z", "elapsed": 5368, "level": "DEBUG", "message": "Word positioned", "data": {"text": "radiance", "x": 873, "y": 270}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.494Z", "elapsed": 5373, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nerf", "x": 867, "y": 316}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.496Z", "elapsed": 5375, "level": "DEBUG", "message": "Word positioned", "data": {"text": "propose", "x": 565, "y": 354}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.501Z", "elapsed": 5380, "level": "DEBUG", "message": "Word positioned", "data": {"text": "reconstruction", "x": 971, "y": 301}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.501Z", "elapsed": 5380, "level": "DEBUG", "message": "Word positioned", "data": {"text": "dataset", "x": 718, "y": 378}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.502Z", "elapsed": 5381, "level": "DEBUG", "message": "Word positioned", "data": {"text": "models", "x": 553, "y": 154}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.503Z", "elapsed": 5382, "level": "DEBUG", "message": "Word positioned", "data": {"text": "rendering", "x": 723, "y": 223}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.504Z", "elapsed": 5383, "level": "DEBUG", "message": "Word positioned", "data": {"text": "machine", "x": 743, "y": 176}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.506Z", "elapsed": 5385, "level": "DEBUG", "message": "Word positioned", "data": {"text": "networks", "x": 891, "y": 129}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.508Z", "elapsed": 5387, "level": "DEBUG", "message": "Word positioned", "data": {"text": "approach", "x": 328, "y": 273}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.510Z", "elapsed": 5389, "level": "DEBUG", "message": "Word positioned", "data": {"text": "depth", "x": 539, "y": 106}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.510Z", "elapsed": 5389, "level": "DEBUG", "message": "Word positioned", "data": {"text": "novel", "x": 314, "y": 365}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.511Z", "elapsed": 5390, "level": "DEBUG", "message": "Word positioned", "data": {"text": "video", "x": 483, "y": 239}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.511Z", "elapsed": 5390, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scene", "x": 844, "y": 153}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.513Z", "elapsed": 5392, "level": "DEBUG", "message": "Word positioned", "data": {"text": "method", "x": 191, "y": 209}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.518Z", "elapsed": 5397, "level": "DEBUG", "message": "Word positioned", "data": {"text": "using", "x": 193, "y": 345}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.518Z", "elapsed": 5397, "level": "DEBUG", "message": "Word positioned", "data": {"text": "images", "x": 346, "y": 220}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.520Z", "elapsed": 5399, "level": "DEBUG", "message": "Word positioned", "data": {"text": "research", "x": 328, "y": 405}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.521Z", "elapsed": 5400, "level": "DEBUG", "message": "Word positioned", "data": {"text": "data", "x": 812, "y": 367}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.522Z", "elapsed": 5401, "level": "DEBUG", "message": "Word positioned", "data": {"text": "results", "x": 902, "y": 389}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.525Z", "elapsed": 5404, "level": "DEBUG", "message": "Word positioned", "data": {"text": "training", "x": 645, "y": 319}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.526Z", "elapsed": 5405, "level": "DEBUG", "message": "Word positioned", "data": {"text": "paper", "x": 678, "y": 97}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.533Z", "elapsed": 5412, "level": "DEBUG", "message": "Word positioned", "data": {"text": "while", "x": 140, "y": 327}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.535Z", "elapsed": 5414, "level": "DEBUG", "message": "Word positioned", "data": {"text": "methods", "x": 1020, "y": 234}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.536Z", "elapsed": 5415, "level": "DEBUG", "message": "Word positioned", "data": {"text": "temporal", "x": 336, "y": 88}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.537Z", "elapsed": 5416, "level": "DEBUG", "message": "Word positioned", "data": {"text": "shape", "x": 991, "y": 139}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.538Z", "elapsed": 5417, "level": "DEBUG", "message": "Word positioned", "data": {"text": "time", "x": 756, "y": 127}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.542Z", "elapsed": 5421, "level": "DEBUG", "message": "Word positioned", "data": {"text": "different", "x": 135, "y": 198}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.543Z", "elapsed": 5422, "level": "DEBUG", "message": "Word positioned", "data": {"text": "such", "x": 385, "y": 321}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.544Z", "elapsed": 5423, "level": "DEBUG", "message": "Word positioned", "data": {"text": "learned", "x": 515, "y": 59}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.545Z", "elapsed": 5424, "level": "DEBUG", "message": "Word positioned", "data": {"text": "multiple", "x": 54, "y": 267}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.547Z", "elapsed": 5426, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representation", "x": 1076, "y": 286}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.548Z", "elapsed": 5427, "level": "DEBUG", "message": "Word positioned", "data": {"text": "via", "x": 779, "y": 87}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.548Z", "elapsed": 5427, "level": "DEBUG", "message": "Word positioned", "data": {"text": "scenes", "x": 585, "y": 395}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.549Z", "elapsed": 5428, "level": "DEBUG", "message": "Word positioned", "data": {"text": "work", "x": 324, "y": 317}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.550Z", "elapsed": 5429, "level": "DEBUG", "message": "Word positioned", "data": {"text": "changes", "x": 700, "y": 424}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.551Z", "elapsed": 5430, "level": "DEBUG", "message": "Word positioned", "data": {"text": "over", "x": 429, "y": 177}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.553Z", "elapsed": 5432, "level": "DEBUG", "message": "Word positioned", "data": {"text": "representations", "x": 1115, "y": 173}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.554Z", "elapsed": 5434, "level": "DEBUG", "message": "Word positioned", "data": {"text": "optimization", "x": 88, "y": 126}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.559Z", "elapsed": 5438, "level": "DEBUG", "message": "Word positioned", "data": {"text": "nerfs", "x": 218, "y": 98}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.560Z", "elapsed": 5439, "level": "DEBUG", "message": "Word positioned", "data": {"text": "existing", "x": 1064, "y": 111}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.561Z", "elapsed": 5440, "level": "DEBUG", "message": "Word positioned", "data": {"text": "demonstrate", "x": 783, "y": 40}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.562Z", "elapsed": 5441, "level": "DEBUG", "message": "Word positioned", "data": {"text": "dreameditor", "x": 99, "y": 407}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.563Z", "elapsed": 5442, "level": "DEBUG", "message": "Word positioned", "data": {"text": "geometry", "x": 286, "y": 47}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.564Z", "elapsed": 5443, "level": "DEBUG", "message": "Word positioned", "data": {"text": "text", "x": 786, "y": 318}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.565Z", "elapsed": 5444, "level": "INFO", "message": "Drawing word cloud", "data": {"wordCount": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.567Z", "elapsed": 5446, "level": "INFO", "message": "Word cloud rendering completed successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.935Z", "elapsed": 5814, "level": "INFO", "message": "Color scheme changed", "data": {"oldScheme": "default", "newScheme": "bright", "element": "color-scheme"}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.936Z", "elapsed": 5815, "level": "DEBUG", "message": "Updating word cloud colors", "data": {"colorScheme": "bright"}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.938Z", "elapsed": 5817, "level": "INFO", "message": "Word cloud colors updated", "data": {"colorScheme": "bright", "elementsUpdated": 50}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.508Z", "elapsed": 6387, "level": "DEBUG", "message": "Search input changed", "data": {"value": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.510Z", "elapsed": 6389, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "t"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.512Z", "elapsed": 6391, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "t", "totalCards": 20, "visibleCount": 20}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.554Z", "elapsed": 6433, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.555Z", "elapsed": 6434, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tr"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.555Z", "elapsed": 6434, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tr", "totalCards": 20, "visibleCount": 19}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.574Z", "elapsed": 6453, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.575Z", "elapsed": 6454, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tra"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.576Z", "elapsed": 6455, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tra", "totalCards": 20, "visibleCount": 18}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.588Z", "elapsed": 6467, "level": "DEBUG", "message": "Search input changed", "data": {"value": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.588Z", "elapsed": 6467, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "tran"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.592Z", "elapsed": 6471, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "tran", "totalCards": 20, "visibleCount": 7}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.622Z", "elapsed": 6501, "level": "DEBUG", "message": "Search input changed", "data": {"value": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.624Z", "elapsed": 6503, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "trans"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.625Z", "elapsed": 6504, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "trans", "totalCards": 20, "visibleCount": 6}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.636Z", "elapsed": 6515, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.637Z", "elapsed": 6516, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transf"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.637Z", "elapsed": 6516, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transf", "totalCards": 20, "visibleCount": 4}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.664Z", "elapsed": 6543, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.665Z", "elapsed": 6544, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfo"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.666Z", "elapsed": 6545, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfo", "totalCards": 20, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.672Z", "elapsed": 6551, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.673Z", "elapsed": 6552, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transfor"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.675Z", "elapsed": 6554, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transfor", "totalCards": 20, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.687Z", "elapsed": 6566, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.688Z", "elapsed": 6567, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transform"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.689Z", "elapsed": 6568, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transform", "totalCards": 20, "visibleCount": 1}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.703Z", "elapsed": 6582, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.703Z", "elapsed": 6582, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transforme"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.704Z", "elapsed": 6583, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transforme", "totalCards": 20, "visibleCount": 0}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.723Z", "elapsed": 6603, "level": "DEBUG", "message": "Search input changed", "data": {"value": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.725Z", "elapsed": 6604, "level": "DEBUG", "message": "Filtering papers", "data": {"searchTerm": "transformer"}, "stack": null}, {"timestamp": "2025-06-05T18:42:21.725Z", "elapsed": 6604, "level": "INFO", "message": "Papers filtered", "data": {"searchTerm": "transformer", "totalCards": 20, "visibleCount": 0}, "stack": null}], "stats": {"totalLogs": 201, "byLevel": {"DEBUG": 139, "INFO": 60, "WARN": 2, "ERROR": 0}, "errors": [], "warnings": [{"timestamp": "2025-06-05T18:42:16.213Z", "elapsed": 1092, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:42:20.263Z", "elapsed": 5142, "level": "WARN", "message": "Container has zero dimensions, retrying in 100ms", "data": {}, "stack": null}]}}, "summary": {"totalTests": 8, "passedTests": 7, "failedTests": 1, "totalErrors": 0, "totalLogs": 202, "screenshots": 3}}