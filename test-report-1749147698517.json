{"timestamp": "2025-06-05T18:21:20.108Z", "tests": [{"name": "Page Title", "passed": true, "timestamp": "2025-06-05T18:21:28.109Z"}, {"name": "Loading Element Visible", "passed": false, "error": "Error: failed to find element matching selector \"#loading\"", "timestamp": "2025-06-05T18:21:28.151Z"}, {"name": "External Dependencies Loaded", "passed": true, "timestamp": "2025-06-05T18:21:28.170Z"}, {"name": "<PERSON>gger Initialized", "passed": true, "timestamp": "2025-06-05T18:21:28.173Z"}, {"name": "Refresh <PERSON><PERSON>", "passed": false, "error": "No element found for selector: #refresh-btn", "timestamp": "2025-06-05T18:21:38.327Z"}, {"name": "Color Scheme Change", "passed": false, "error": "No element found for selector: #color-scheme", "timestamp": "2025-06-05T18:21:38.339Z"}, {"name": "Search Functionality", "passed": false, "error": "No element found for selector: #paper-search", "timestamp": "2025-06-05T18:21:38.346Z"}, {"name": "Download Logs Button", "passed": false, "timestamp": "2025-06-05T18:21:38.359Z"}], "logs": [{"type": "warning", "text": "cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation", "timestamp": "2025-06-05T18:21:25.406Z"}, {"type": "log", "text": "[INFO] <PERSON><PERSON> initialized J<PERSON><PERSON><PERSON>le@object", "timestamp": "2025-06-05T18:21:25.895Z"}, {"type": "log", "text": "[INFO] DOM Content Loaded JSHandle@object", "timestamp": "2025-06-05T18:21:26.020Z"}, {"type": "log", "text": "[INFO] All UI elements found successfully JSHandle@object", "timestamp": "2025-06-05T18:21:26.024Z"}, {"type": "log", "text": "[INFO] Checking external dependencies JSHandle@object", "timestamp": "2025-06-05T18:21:26.027Z"}, {"type": "error", "text": "[ERROR] D3 word cloud layout not available JSHandle@object", "timestamp": "2025-06-05T18:21:26.028Z"}, {"type": "error", "text": "[ERROR] Critical error during initialization JSHandle@object", "timestamp": "2025-06-05T18:21:26.029Z"}], "screenshots": [{"name": "01-initial-load", "path": "screenshot-01-initial-load-1749147687994.png", "timestamp": "2025-06-05T18:21:28.105Z"}, {"name": "02-data-load-timeout", "path": "screenshot-02-data-load-timeout-1749147698185.png", "timestamp": "2025-06-05T18:21:38.310Z"}, {"name": "03-after-interactions", "path": "screenshot-03-after-interactions-1749147698359.png", "timestamp": "2025-06-05T18:21:38.494Z"}], "errors": [{"type": "page-error", "message": "Cannot read properties of null (reading 'classList')", "stack": "TypeError: Cannot read properties of null (reading 'classList')\n    at eval (:8:32)\n    at <anonymous> (pptr:evaluateHandle;WaitTask.rerun%20(C%3A%5CUsers%5CM%5CDesktop%5Ctest2%5Cnode_modules%5Cpuppeteer-core%5Clib%5Ccjs%5Cpuppeteer%5Ccommon%5CWaitTask.js%3A70%3A54):4:36)\n    at start (pptr:internal:3:3773)\n    at <anonymous> (pptr:evaluate;WaitTask.rerun%20(C%3A%5CUsers%5CM%5CDesktop%5Ctest2%5Cnode_modules%5Cpuppeteer-core%5Clib%5Ccjs%5Cpuppeteer%5Ccommon%5CWaitTask.js%3A100%3A32):2:29)", "timestamp": "2025-06-05T18:21:28.190Z"}], "performance": {"pageLoad": 6031}, "applicationLogs": {"logs": [{"timestamp": "2025-06-05T18:21:25.738Z", "elapsed": 1, "level": "INFO", "message": "<PERSON><PERSON> initialized", "data": {"timestamp": "2025-06-05T18:21:25.738Z"}, "stack": null}, {"timestamp": "2025-06-05T18:21:25.955Z", "elapsed": 218, "level": "INFO", "message": "DOM Content Loaded", "data": {"readyState": "interactive", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/121.0.6167.85 Safari/537.36"}, "stack": null}, {"timestamp": "2025-06-05T18:21:25.956Z", "elapsed": 219, "level": "INFO", "message": "All UI elements found successfully", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:21:25.957Z", "elapsed": 220, "level": "INFO", "message": "Checking external dependencies", "data": {}, "stack": null}, {"timestamp": "2025-06-05T18:21:25.957Z", "elapsed": 220, "level": "ERROR", "message": "D3 word cloud layout not available", "data": {}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:188:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:303:28)"}, {"timestamp": "2025-06-05T18:21:25.958Z", "elapsed": 221, "level": "ERROR", "message": "Critical error during initialization", "data": {"error": "D3 word cloud layout is required but not available", "stack": "Error: D3 word cloud layout is required but not available\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:304:27)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:188:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:783:24)"}], "stats": {"totalLogs": 6, "byLevel": {"DEBUG": 0, "INFO": 4, "WARN": 0, "ERROR": 2}, "errors": [{"timestamp": "2025-06-05T18:21:25.957Z", "elapsed": 220, "level": "ERROR", "message": "D3 word cloud layout not available", "data": {}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:188:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:303:28)"}, {"timestamp": "2025-06-05T18:21:25.958Z", "elapsed": 221, "level": "ERROR", "message": "Critical error during initialization", "data": {"error": "D3 word cloud layout is required but not available", "stack": "Error: D3 word cloud layout is required but not available\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:304:27)"}, "stack": "Error\n    at Logger.log (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:188:48)\n    at HTMLDocument.<anonymous> (file:///C:/Users/<USER>/Desktop/test2/wordcloud.html:783:24)"}], "warnings": []}}, "summary": {"totalTests": 8, "passedTests": 3, "failedTests": 5, "totalErrors": 1, "totalLogs": 7, "screenshots": 3}}