<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DICOM Viewer</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/dicom-parser@1.8.15/dist/dicomParser.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script
        src="https://cdn.jsdelivr.net/npm/cornerstone-wado-image-loader@4.1.1/dist/cornerstoneWADOImageLoader.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cornerstone-math@0.1.8/dist/cornerstoneMath.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/cornerstone-tools@6.0.4/dist/cornerstoneTools.min.js"></script>
    <style>
        #dicomImage {
            width: 100%;
            height: 100%;
            background-color: #000;
        }

        .dicom-container {
            position: relative;
            width: 100%;
            height: 70vh;
            border: 1px solid #374151;
            border-radius: 0.5rem;
            overflow: hidden;
        }

        .metadata-panel {
            max-height: 300px;
            overflow-y: auto;
        }

        .tooltip {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 100;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 4px solid #fff;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>
</head>

<body class="bg-gray-900 text-gray-200 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <header class="mb-8">
            <h1 class="text-3xl font-bold text-blue-400 mb-2">DICOM Viewer</h1>
            <p class="text-gray-400">A lightweight DICOM medical imaging viewer</p>
        </header>

        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <div class="lg:col-span-3">
                <div class="bg-gray-800 rounded-lg shadow-lg p-4">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-semibold">Image Viewer</h2>
                        <div class="flex space-x-2">
                            <button id="resetView" class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">
                                Reset View
                            </button>
                            <button id="invert" class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">
                                Invert
                            </button>
                            <button id="zoomIn" class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">
                                Zoom In
                            </button>
                            <button id="zoomOut" class="bg-gray-700 hover:bg-gray-600 px-3 py-1 rounded text-sm">
                                Zoom Out
                            </button>
                        </div>
                    </div>

                    <div class="dicom-container relative">
                        <div id="loadingOverlay" class="loading-overlay hidden">
                            <div class="spinner"></div>
                        </div>
                        <div id="dicomImage"></div>
                        <div id="tooltip" class="tooltip hidden"></div>
                    </div>

                    <div class="mt-4">
                        <div class="flex items-center space-x-4">
                            <div class="w-full">
                                <label class="block text-sm font-medium mb-1">Window Width</label>
                                <input id="wwInput" type="range" min="1" max="4000" value="400"
                                    class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="wwValue" class="text-xs text-gray-400">400</span>
                            </div>
                            <div class="w-full">
                                <label class="block text-sm font-medium mb-1">Window Level</label>
                                <input id="wlInput" type="range" min="-1000" max="3000" value="50"
                                    class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer">
                                <span id="wlValue" class="text-xs text-gray-400">50</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="lg:col-span-1">
                <div class="bg-gray-800 rounded-lg shadow-lg p-4 h-full">
                    <h2 class="text-xl font-semibold mb-4">File & Metadata</h2>

                    <div class="mb-6">
                        <label class="block text-sm font-medium mb-2">Load DICOM File</label>
                        <input type="file" id="fileInput" accept=".dcm,.dicom" class="block w-full text-sm text-gray-400
                            file:mr-4 file:py-2 file:px-4
                            file:rounded-lg file:border-0
                            file:text-sm file:font-semibold
                            file:bg-blue-600 file:text-white
                            hover:file:bg-blue-700
                            cursor-pointer
                            bg-gray-700 rounded-lg
                        ">
                    </div>

                    <div class="mb-4">
                        <h3 class="font-medium mb-2">Patient Information</h3>
                        <div class="bg-gray-700 p-3 rounded-lg">
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <div>Name:</div>
                                <div id="patientName" class="text-blue-300">-</div>
                                <div>ID:</div>
                                <div id="patientId" class="text-blue-300">-</div>
                                <div>Sex:</div>
                                <div id="patientSex" class="text-blue-300">-</div>
                                <div>Age:</div>
                                <div id="patientAge" class="text-blue-300">-</div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h3 class="font-medium mb-2">Study Information</h3>
                        <div class="bg-gray-700 p-3 rounded-lg">
                            <div class="grid grid-cols-2 gap-2 text-sm">
                                <div>Study Date:</div>
                                <div id="studyDate" class="text-blue-300">-</div>
                                <div>Modality:</div>
                                <div id="modality" class="text-blue-300">-</div>
                                <div>Description:</div>
                                <div id="studyDescription" class="text-blue-300">-</div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button id="showMetadata" class="bg-gray-700 hover:bg-gray-600 w-full py-2 rounded text-sm">
                            Show Full Metadata
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Metadata Modal -->
    <div id="metadataModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 hidden">
        <div class="bg-gray-800 rounded-lg w-11/12 max-w-4xl max-h-[90vh] flex flex-col">
            <div class="p-4 border-b border-gray-700 flex justify-between items-center">
                <h3 class="text-lg font-semibold">DICOM Metadata</h3>
                <button id="closeModal" class="text-gray-400 hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="p-4 metadata-panel overflow-auto">
                <pre id="fullMetadata" class="text-xs text-gray-300"></pre>
            </div>
        </div>
    </div>

    <script>
        // Initialize Cornerstone
        document.addEventListener('DOMContentLoaded', function () {
            // Configure cornerstone
            cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
            cornerstoneWADOImageLoader.external.dicomParser = dicomParser;
            cornerstoneWADOImageLoader.webWorkerManager.initialize({
                maxWebWorkers: navigator.hardwareConcurrency || 4,
                startWebWorkersOnDemand: true,
            });

            // Enable the element
            const element = document.getElementById('dicomImage');
            cornerstone.enable(element);

            // Try to load export.dcm automatically
            fetch('export.dcm')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load DICOM file');
                    }
                    return response.blob();
                })
                .then(blob => {
                    const file = new File([blob], 'export.dcm', { type: 'application/dicom' });
                    const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
                    loadAndViewImage(imageId);
                })
                .catch(error => {
                    console.error('Error loading DICOM file:', error);
                    alert('Failed to load DICOM file. Please check console for details.');
                });

            // Try to load export.dcm automatically
            fetch('export.dcm')
                .then(response => response.blob())
                .then(blob => {
                    const file = new File([blob], 'export.dcm');
                    const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
                    loadAndViewImage(imageId);
                })
                .catch(error => {
                    console.log('Auto-load failed, waiting for manual file selection:', error);
                });

            // File input handler
            document.getElementById('fileInput').addEventListener('change', function (e) {
                const file = e.target.files[0];
                if (!file) return;

                showLoading(true);

                const imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
                loadAndViewImage(imageId);
            });

            // Tool buttons
            document.getElementById('resetView').addEventListener('click', function () {
                const element = document.getElementById('dicomImage');
                const viewport = cornerstone.getViewport(element);
                viewport.voi.windowWidth = 400;
                viewport.voi.windowCenter = 50;
                viewport.scale = 1;
                viewport.translation.x = 0;
                viewport.translation.y = 0;
                cornerstone.setViewport(element, viewport);
                updateSliderValues(400, 50);
            });

            document.getElementById('invert').addEventListener('click', function () {
                const element = document.getElementById('dicomImage');
                const viewport = cornerstone.getViewport(element);
                viewport.invert = !viewport.invert;
                cornerstone.setViewport(element, viewport);
            });

            document.getElementById('zoomIn').addEventListener('click', function () {
                const element = document.getElementById('dicomImage');
                const viewport = cornerstone.getViewport(element);
                viewport.scale *= 1.2;
                cornerstone.setViewport(element, viewport);
            });

            document.getElementById('zoomOut').addEventListener('click', function () {
                const element = document.getElementById('dicomImage');
                const viewport = cornerstone.getViewport(element);
                viewport.scale /= 1.2;
                cornerstone.setViewport(element, viewport);
            });

            // Window width/level sliders
            const wwInput = document.getElementById('wwInput');
            const wlInput = document.getElementById('wlInput');
            const wwValue = document.getElementById('wwValue');
            const wlValue = document.getElementById('wlValue');

            wwInput.addEventListener('input', function () {
                wwValue.textContent = this.value;
                updateViewport();
            });

            wlInput.addEventListener('input', function () {
                wlValue.textContent = this.value;
                updateViewport();
            });

            function updateViewport() {
                const element = document.getElementById('dicomImage');
                const viewport = cornerstone.getViewport(element);
                viewport.voi.windowWidth = parseInt(wwInput.value);
                viewport.voi.windowCenter = parseInt(wlInput.value);
                cornerstone.setViewport(element, viewport);
            }

            function updateSliderValues(ww, wl) {
                wwInput.value = ww;
                wlInput.value = wl;
                wwValue.textContent = ww;
                wlValue.textContent = wl;
            }

            // Metadata modal
            document.getElementById('showMetadata').addEventListener('click', function () {
                document.getElementById('metadataModal').classList.remove('hidden');
            });

            document.getElementById('closeModal').addEventListener('click', function () {
                document.getElementById('metadataModal').classList.add('hidden');
            });

            // Tooltip for pixel values
            const tooltip = document.getElementById('tooltip');
            element.addEventListener('mousemove', function (e) {
                const rect = element.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const enabledElement = cornerstone.getEnabledElement(element);
                if (!enabledElement || !enabledElement.image) {
                    tooltip.classList.add('hidden');
                    return;
                }

                const image = enabledElement.image;
                const pixelData = cornerstone.getPixels(element, x, y, 1, 1);
                const pixelValue = pixelData[0];

                tooltip.style.left = `${e.clientX + 10}px`;
                tooltip.style.top = `${e.clientY + 10}px`;
                tooltip.textContent = `X: ${Math.round(x)}, Y: ${Math.round(y)}, Value: ${pixelValue}`;
                tooltip.classList.remove('hidden');
            });

            element.addEventListener('mouseout', function () {
                tooltip.classList.add('hidden');
            });

            // Loading overlay
            function showLoading(show) {
                const overlay = document.getElementById('loadingOverlay');
                overlay.classList.toggle('hidden', !show);
            }
        });

        // Load and display the DICOM image
        function loadAndViewImage(imageId) {
            const element = document.getElementById('dicomImage');

            cornerstone.loadImage(imageId).then(function (image) {
                // Display the image
                cornerstone.displayImage(element, image);

                // Set default viewport settings
                const viewport = cornerstone.getDefaultViewportForImage(element, image);
                cornerstone.setViewport(element, viewport);

                // Update slider values
                updateSliderValues(viewport.voi.windowWidth, viewport.voi.windowCenter);

                // Extract and display metadata
                displayMetadata(image.data);

                showLoading(false);
            }).catch(function (error) {
                console.error('Error loading image:', error);
                showLoading(false);
                alert('Error loading DICOM file. Please check the console for details.');
            });
        }

        // Display DICOM metadata
        function displayMetadata(data) {
            // Patient information
            document.getElementById('patientName').textContent =
                data.string('x00100010') || '-';
            document.getElementById('patientId').textContent =
                data.string('x00100020') || '-';
            document.getElementById('patientSex').textContent =
                data.string('x00100040') || '-';
            document.getElementById('patientAge').textContent =
                data.string('x00101010') || '-';

            // Study information
            document.getElementById('studyDate').textContent =
                data.string('x00080020') || '-';
            document.getElementById('modality').textContent =
                data.string('x00080060') || '-';
            document.getElementById('studyDescription').textContent =
                data.string('x00081030') || '-';

            // Full metadata for modal
            const metadata = {};
            data.elements.x7fe00010 = undefined; // Remove pixel data for display

            for (const tag in data.elements) {
                const element = data.elements[tag];
                metadata[tag] = {
                    vr: element.vr,
                    length: element.length,
                    dataOffset: element.dataOffset,
                    description: dicomParser.lookupTag(tag).description
                };
            }

            document.getElementById('fullMetadata').textContent =
                JSON.stringify(metadata, null, 2);
        }

        // Helper function to update slider values
        function updateSliderValues(ww, wl) {
            document.getElementById('wwInput').value = ww;
            document.getElementById('wlInput').value = wl;
            document.getElementById('wwValue').textContent = ww;
            document.getElementById('wlValue').textContent = wl;
        }
    </script>
</body>

</html>